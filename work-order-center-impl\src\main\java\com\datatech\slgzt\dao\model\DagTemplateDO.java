package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * DAG模板 DO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("WOC_DAG_TEMPLATE")
public class DagTemplateDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableField("ID")
    private String id;

    /**
     * 模板名称
     */
    @TableField("NAME")
    private String name;

    /**
     * 模板描述
     */
    @TableField("DESCRIPTION")
    private String description;

    /**
     * 前端保存的DAG画布数据
     */
    @TableField("DAG_CANVAS")
    private String dagCanvas;

    /**
     * VPC资源列表JSON
     */
    @TableField("VPC_MODEL_LIST")
    private String vpcModelListStr;

    /**
     * ECS资源列表JSON
     */
    @TableField("ECS_MODEL_LIST")
    private String ecsModelListStr;

    /**
     * MySQL资源列表JSON
     */
    @TableField("MYSQL_MODEL_LIST")
    private String mysqlModelListStr;

    /**
     * Redis资源列表JSON
     */
    @TableField("REDIS_MODEL_LIST")
    private String redisModelListStr;

    /**
     * GCS资源列表JSON
     */
    @TableField("GCS_MODEL_LIST")
    private String gcsModelListStr;

    /**
     * EVS资源列表JSON
     */
    @TableField("EVS_MODEL_LIST")
    private String evsModelListStr;

    /**
     * EIP资源列表JSON
     */
    @TableField("EIP_MODEL_LIST")
    private String eipModelListStr;

    /**
     * NAT网关资源列表JSON
     */
    @TableField("NAT_MODEL_LIST")
    private String natModelListStr;

    /**
     * SLB资源列表JSON
     */
    @TableField("SLB_MODEL_LIST")
    private String slbModelListStr;

    /**
     * OBS资源列表JSON
     */
    @TableField("OBS_MODEL_LIST")
    private String obsModelListStr;

    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建人ID
     */
    @TableField("CREATOR_ID")
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;
} 