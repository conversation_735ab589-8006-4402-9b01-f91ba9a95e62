package com.datatech.slgzt.model.nostander;

import lombok.Data;

import java.util.List;

@Data
public class NetworkModel2 extends PNatModel{


    // todo 不需要
    private String networkId;
    // todo 不需要
    private String regionCode;

    /**
     * 网络名称
     */
    private String name;

    /**
     * 网络描述
     */
    // todo 不需要
    private String description;

    /**
     * 工单Id
     */
    // todo 不需要
    private String orderId;

    /**
     * 网络平面
     */
    private String plane;

    /**
     * 租户id
     */
    // todo 不需要
    private Long tenantId;

    private String networkType;

    // todo 不需要
    private String systemSource;

    // todo 不需要
    private String instanceId;
    private String vlanId;
    private String vlan;
    // todo 不需要
    private String functionalModule;
    // todo 不需要
    private String azCode;

    // todo 不需要
    private String catalogueDomainCode;

    // todo 不需要
    private String catalogueDomainName;

    // todo 不需要
    private String domainCode;

    // todo 不需要
    private String domainName;
    private String detail;

    private List<SubnetModel> subnets;


}