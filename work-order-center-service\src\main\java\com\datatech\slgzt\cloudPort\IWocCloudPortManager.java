package com.datatech.slgzt.cloudPort;


import com.datatech.slgzt.model.dto.cloudPort.CloudPortDTO;

import com.datatech.slgzt.model.query.WocCloudPortQuery;
import com.datatech.slgzt.utils.PageResult;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-31
 */
public interface IWocCloudPortManager {

    /**
     * 新增
     */
    void create(CloudPortDTO dto);


    /**
     * 删除
     */
    void delete(String id);


    /**
     * 云端口详情
     * @param id
     * @return
     */
    CloudPortDTO queryWocCloudPortDetail(String id);


    /**
     * 分页查询
     */
    PageResult<CloudPortDTO> page(WocCloudPortQuery query);

}
