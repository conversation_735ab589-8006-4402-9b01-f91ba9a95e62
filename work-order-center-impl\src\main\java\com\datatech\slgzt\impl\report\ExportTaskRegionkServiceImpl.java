package com.datatech.slgzt.impl.report;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.component.FtpConfig;
import com.datatech.slgzt.dao.RegionCmdbDAO;
import com.datatech.slgzt.dao.model.RegionCmdbDO;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.RegionManager;
import com.datatech.slgzt.manager.VropsRegionManager;
import com.datatech.slgzt.model.dto.RegionDTO;
import com.datatech.slgzt.model.dto.VropsRegionDTO;
import com.datatech.slgzt.model.query.RegionQuery;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.utils.FTPUtil;
import com.datatech.slgzt.convert.ExportTaskServiceConvert;
import com.datatech.slgzt.dao.ComResUsageDAO;
import com.datatech.slgzt.dao.mapper.ComResUsageMapper;
import com.datatech.slgzt.dao.model.report.ComResUsageDO;
import com.datatech.slgzt.manager.ExportTaskManager;
import com.datatech.slgzt.model.dto.ExportTaskDTO;
import com.datatech.slgzt.model.opm.ExportTaskOpm;
import com.datatech.slgzt.model.query.ComResUsageQuery;
import com.datatech.slgzt.model.report.RegionReportExcelDTO;
import com.datatech.slgzt.service.ExportTaskService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.StreamUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.io.File;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.io.IOException;

/**
 * <AUTHOR>
 * @description 资源池报表导出实现
 * @date 2025年 05月26日 15:01:37
 */
@Slf4j
@Service
public class ExportTaskRegionkServiceImpl implements ExportTaskService {

    ExecutorService executor = Executors.newFixedThreadPool(5);
    @Resource
    private ExportTaskManager exportTaskManager;

    @Resource
    private ExportTaskServiceConvert serviceConvert;

    @Resource
    private RegionManager regionManager;

    @Resource
    private VropsRegionManager vropsRegionManager;

    @Resource
    private ComResUsageDAO comResUsageDAO;

    @Resource
    private RegionCmdbDAO regionCmdbDAO;

    @Resource
    private FtpConfig ftpConfig;

    private static final String EXPORT_PATH = "export/region/";
    private static final String BUSINESS_FILE_NAME = "REION_REPORT";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");



    @Override
    public void export(ExportTaskOpm opm) {
        opm.setRegionIds(getRegionIds(opm));
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        // 创建导出任务记录
        ExportTaskDTO taskDTO = new ExportTaskDTO();
        taskDTO.setReportName(opm.getReportName());
        taskDTO.setBusinessType(getReportType());
        taskDTO.setStatType(opm.getStatType());
        taskDTO.setStartTime(opm.getStartTime());
        taskDTO.setEndTime(opm.getEndTime());
        taskDTO.setCreator(currentUser.getUserName());
        taskDTO.setQueryCondition(opm.getQueryCondition());
        taskDTO.setStatus(0); // 0-生成中
        taskDTO.setCreateTime(LocalDateTime.now());
        taskDTO.setExportFields(JSON.toJSONString(opm.getExportFields())); // 保存导出字段列表
        String taskId = exportTaskManager.createTask(taskDTO);
        taskDTO.setId(taskId);
        executor.execute(() -> {
            // 生成Excel文件
            String fileName = generateExcelFileName();
            String filePath = EXPORT_PATH + fileName;
            // 设置任务的文件名
            taskDTO.setFileName(fileName);
            // 确保导出目录存在
            File exportDir = new File(EXPORT_PATH);
            if (!exportDir.exists()) {
                exportDir.mkdirs();
            }
            try {
                List<Long> regionIds = opm.getRegionIds();
                // 这里 需要指定写用哪个class去写
                ExcelWriter excelWriter = EasyExcel.write(filePath, RegionReportExcelDTO.class).build();
                // 这里注意 如果同一个sheet只要创建一次
                WriteSheet writeSheet = EasyExcel.writerSheet("资源使用情况").build();
                regionIds.forEach(regionId -> {
                    // 查询数据
                    List<ComResUsageDO> resUsageDOS = comResUsageDAO.list(new ComResUsageQuery()
                            .setRegionId(regionId)
                            .setStartTime(opm.getStartTime())
                            .setEndTime(opm.getEndTime())
                    );
                    //整理出codeList
                    List<String> codeList = resUsageDOS.stream()
                                                       .map(ComResUsageDO::getRegionCode)
                                                       .distinct()
                                                       .collect(Collectors.toList());
                    List<RegionCmdbDO> regionCmdbDOS=new ArrayList<>();
                    if (ObjNullUtils.isNotNull(codeList)){
                        regionCmdbDOS = regionCmdbDAO.listByCodeList(codeList);
                    }
                    Map<String, String> cmdbMap = StreamUtils.toMap(regionCmdbDOS, RegionCmdbDO::getCode, RegionCmdbDO::getVirtualPoolName);
                    List<RegionReportExcelDTO> regionReportExcelDTOS = StreamUtils.mapArray(resUsageDOS, serviceConvert::convert);
                    regionReportExcelDTOS.forEach(item -> {
                        item.setVirtualRegionName(cmdbMap.get(item.getRegionCode()));
                    });
                    //按照平台排序
                    regionReportExcelDTOS.sort(Comparator.comparing(RegionReportExcelDTO::getPlatform));
                    String statType = opm.getStatType();
                    Map<String, List<RegionReportExcelDTO>> groupedByTime = groupDataByTime(regionReportExcelDTOS, statType);
                    calculateAndSetPeakValues(groupedByTime, statType);
                    List<RegionReportExcelDTO> exportList = getExportList(groupedByTime);
                    excelWriter.write(exportList, writeSheet);
                });
                excelWriter.finish();
                //ftp上传
                // 创建FTP工具类实例
                FTPUtil ftpUtil = new FTPUtil(ftpConfig.getIp(), ftpConfig.getPort(), ftpConfig.getUser(), ftpConfig.getPass());
                // 构建远程FTP路径
                String remotePath = ftpConfig.getBasePath() + new SimpleDateFormat("yyyy-MM-dd/").format(new Date());
                // 上传文件到FTP
                File localFile = new File(filePath);
                boolean uploadResult = ftpUtil.uploadFile(remotePath, localFile);
                if (uploadResult) {
                    // 上传成功,返回FTP上的完整路径
                    String ftpFilePath = remotePath + localFile.getName();
                    // 删除本地临时文件
                    // localFile.delete();
                    // 更新任务记录中的文件路径和文件名
                    taskDTO.setFilePath(ftpFilePath);
                    taskDTO.setFileName(localFile.getName());
                    exportTaskManager.updateTask(taskDTO);
                }
                // 更新任务状态为完成
                taskDTO.setStatus(1); // 1-完成
                exportTaskManager.updateTask(taskDTO);
                log.info("Export completed successfully. File path: {}", filePath);
            } catch (Exception e) {
                log.error("Export failed", e);
                // 更新任务状态为失败
                taskDTO.setStatus(2); // 2-失败
                exportTaskManager.updateTask(taskDTO);
            }
        });

    }

    /**
     * 处理数据并导出Excel
     *
     * @param regionReportExcelDTOS 原始数据列表
     * @param opm                   导出参数
     * @param taskDTO              导出任务DTO
     * @return 导出文件路径
     */
    private String processDataAndExportExcel(List<RegionReportExcelDTO> regionReportExcelDTOS, ExportTaskOpm opm, ExportTaskDTO taskDTO) {
        String statType = opm.getStatType();

        // 如果没有指定regionId，先按资源池分组，再按时间分组
        Map<Long, List<RegionReportExcelDTO>> groupedByRegion = regionReportExcelDTOS.stream()
                                                                                       .collect(Collectors.groupingBy(RegionReportExcelDTO::getRegionId));

        List<RegionReportExcelDTO> finalExportList = new ArrayList<>();

        // 对每个资源池的数据单独处理
        for (List<RegionReportExcelDTO> regionData : groupedByRegion.values()) {
            Map<String, List<RegionReportExcelDTO>> groupedByTime = groupDataByTime(regionData, statType);
            calculateAndSetPeakValues(groupedByTime, statType);
            finalExportList.addAll(getExportList(groupedByTime));
        }

        // 导出Excel并上传到FTP
        String localFilePath = exportToExcel(finalExportList, opm.getExportFields(), taskDTO);

        try {
            // 创建FTP工具类实例
            FTPUtil ftpUtil = new FTPUtil(ftpConfig.getIp(), ftpConfig.getPort(), ftpConfig.getUser(), ftpConfig.getPass());

            // 构建远程FTP路径
            String remotePath = ftpConfig.getBasePath() + new SimpleDateFormat("yyyy-MM-dd/").format(new Date());
            // 上传文件到FTP
            File localFile = new File(localFilePath);
            boolean uploadResult = ftpUtil.uploadFile(remotePath, localFile);

            if (uploadResult) {
                // 上传成功,返回FTP上的完整路径
                String ftpFilePath = remotePath + localFile.getName();
                // 删除本地临时文件
               // localFile.delete();

                // 更新任务记录中的文件路径和文件名
                taskDTO.setFilePath(ftpFilePath);
                taskDTO.setFileName(localFile.getName());
                exportTaskManager.updateTask(taskDTO);

                return ftpFilePath;
            } else {
                log.error("Failed to upload file to FTP server");
                throw new RuntimeException("Failed to upload file to FTP server");
            }
        } catch (IOException e) {
            log.error("Error uploading file to FTP: ", e);
            throw new RuntimeException("Error uploading file to FTP: " + e.getMessage());
        }
    }

    /**
     * 按时间分组数据
     */
    private Map<String, List<RegionReportExcelDTO>> groupDataByTime(List<RegionReportExcelDTO> dataList, String statType) {
        return dataList.stream()
                       .collect(Collectors.groupingBy(dto -> getTimeKey(dto.getCreateTime(), statType)));
    }

    /**
     * 计算并设置峰值
     */
    private void calculateAndSetPeakValues(Map<String, List<RegionReportExcelDTO>> groupedData, String statType) {
        for (Map.Entry<String, List<RegionReportExcelDTO>> entry : groupedData.entrySet()) {
            List<RegionReportExcelDTO> dataList = entry.getValue();

            // 计算VCPU使用率峰值
            BigDecimal vcpuUsagePeak = dataList.stream()
                                               .map(RegionReportExcelDTO::getVcpuAllocationRate)
                                               .filter(Objects::nonNull)
                                               .max(BigDecimal::compareTo)
                                               .orElse(null);

            // 计算内存使用率峰值
            BigDecimal memoryUsagePeak = dataList.stream()
                                                 .map(RegionReportExcelDTO::getMemoryAllocationRate)
                                                 .filter(Objects::nonNull)
                                                 .max(BigDecimal::compareTo)
                                                 .orElse(null);

            // 计算存储使用率峰值
            BigDecimal storageUsagePeak = dataList.stream()
                                                  .map(RegionReportExcelDTO::getStorageAllocationRate)
                                                  .filter(Objects::nonNull)
                                                  .max(BigDecimal::compareTo)
                                                  .orElse(null);
            //计算使用率均值
            List<BigDecimal> vcpuUsageRateList = dataList.stream()
                                                .map(RegionReportExcelDTO::getVcpuAllocationRate)
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList());
            BigDecimal vcpuUsageAvg = vcpuUsageRateList.stream()
                                               .reduce(BigDecimal.ZERO, BigDecimal::add)
                                                  .divide(new BigDecimal(vcpuUsageRateList.size()), 2, RoundingMode.HALF_UP);
            List<BigDecimal> memoryUsageRateList = dataList.stream()
                                              .map(RegionReportExcelDTO::getMemoryAllocationRate)
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList());
            BigDecimal memoryUsageAvg = memoryUsageRateList.stream()
                                               .reduce(BigDecimal.ZERO, BigDecimal::add)
                                               .divide(new BigDecimal(memoryUsageRateList.size()), 2, RoundingMode.HALF_UP);
            List<BigDecimal> storageUsageRateList = dataList.stream()
                                               .map(RegionReportExcelDTO::getStorageAllocationRate)
                                               .filter(Objects::nonNull)
                                               .collect(Collectors.toList());
            BigDecimal storageUsageAvg = storageUsageRateList.stream()
                                                .reduce(BigDecimal.ZERO, BigDecimal::add)
                                                .divide(new BigDecimal(storageUsageRateList.size()), 2, RoundingMode.HALF_UP);

            // 设置峰值到每个DTO对象
            dataList.forEach(dto -> {
                dto.setVcpuUsagePeak(vcpuUsagePeak);
                dto.setMemoryUsagePeak(memoryUsagePeak);
                dto.setStorageUsagePeak(storageUsagePeak);
                dto.setVcpuUsageAvg(vcpuUsageAvg);
                dto.setMemoryUsageAvg(memoryUsageAvg);
                dto.setStorageUsageAvg(storageUsageAvg);
                // 设置日期格式
                dto.setDataTime(getTimeKey(dto.getCreateTime(), statType));
            });
        }
    }

    /**
     * 获取最终导出列表
     */
    private List<RegionReportExcelDTO> getExportList(Map<String, List<RegionReportExcelDTO>> groupedData) {
        List<RegionReportExcelDTO> exportList = new ArrayList<>();
        // 取出集合里时间最大的那一条
        for (Map.Entry<String, List<RegionReportExcelDTO>> entry : groupedData.entrySet()) {
            List<RegionReportExcelDTO> dataList = entry.getValue();
            dataList.stream()
                    .max(Comparator.comparing(RegionReportExcelDTO::getCreateTime))
                    .ifPresent(exportList::add);
            //按照时间降序排列
            exportList.sort(Comparator.comparing(RegionReportExcelDTO::getCreateTime).reversed());
        }
        return exportList;
    }

    /**
     * 导出数据到Excel文件
     */
    private String exportToExcel(List<RegionReportExcelDTO> exportList, List<String> exportFields, ExportTaskDTO taskDTO) {
        // 生成Excel文件
        String fileName = generateExcelFileName();
        String filePath = EXPORT_PATH + fileName;

        // 设置任务的文件名
        taskDTO.setFileName(fileName);

        // 确保导出目录存在
        File exportDir = new File(EXPORT_PATH);
        if (!exportDir.exists()) {
            exportDir.mkdirs();
        }
        //file
        EasyExcel.write(filePath, RegionReportExcelDTO.class)
                 .includeColumnFiledNames(exportFields)
                 .sheet("资源使用情况").doWrite(exportList);
        return filePath;
    }





    private String generateExcelFileName() {
        String timestamp = LocalDateTime.now().format(DATE_FORMATTER);
        return String.format("%s_%s.xlsx", BUSINESS_FILE_NAME, timestamp);
    }

    private String getTimeKey(LocalDateTime dateTime, String statType) {
        if (dateTime == null) {
            return "unknown";
        }

        switch (statType.toUpperCase()) {
            case "HOUR":
                return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:00"));
            case "DAY":
                return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            case "MONTH":
                return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            default:
                return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
    }

    private List<Long> getRegionIds(ExportTaskOpm opm) {
        // 如果regionIds包含-1，表示查询所有区域
        if (opm.getRegionIds().contains(-1L)) {
            List<RegionDTO> list = regionManager.list(new RegionQuery().setDomainCodes(opm.getDomainCodes()));
            if (ObjNullUtils.isNotNull(opm.getDomainCodes())&&opm.getDomainCodes().contains(CatalogueDomain.VMWARE.getCode())){
                List<VropsRegionDTO> vropsRegionDTOS = vropsRegionManager.listAll();
                vropsRegionDTOS.forEach(vropsRegionDTO -> {
                    RegionDTO regionVO = new RegionDTO();
                    regionVO.setId(vropsRegionDTO.getId());
                    regionVO.setName(vropsRegionDTO.getName());
                    list.add(regionVO);
                });
            }
            return StreamUtils.mapArray(list, RegionDTO::getId);
        }
        // 否则返回指定的regionIds
        return opm.getRegionIds();
    }

    @Override
    public String getReportType() {
        return "REGION";
    }
}
