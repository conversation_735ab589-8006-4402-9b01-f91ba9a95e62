package com.datatech.slgzt.model.req.dag;

import com.datatech.slgzt.model.dto.DagTemplateProductDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * DAG模板产品创建请求
 */
@Data
public class DagTemplateProductCreateReq {

    @NotBlank(message = "模板ID不能为空")
    private String templateId;

    @NotBlank(message = "模板名称不能为空")
    private String templateName;

    private List<String> domainCodeList;

    private List<String> catalogueDomainCodeList;

    private List<DagTemplateProductDTO.ReginModel> reginModelList;

    private String businessSystemId;

    private String businessSystemName;


} 