package com.datatech.slgzt.convert;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.model.BaseProductModel;
import com.datatech.slgzt.model.dto.DagOrderDTO;
import com.datatech.slgzt.model.dto.DagTemplateDTO;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.opm.DagOrderOpm;
import com.datatech.slgzt.model.opm.ProductGeneralCheckOpm;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface DagOrderServiceConvert {


    default DagOrderDTO opm2Order(DagOrderOpm opm,DagTemplateDTO dagTemplateDTO){
        DagOrderDTO dagOrderDTO = new DagOrderDTO();
        dagOrderDTO.setId(OrderTypeEnum.DAG.getPrefix() + "-" + IdUtil.nanoId());
        dagOrderDTO.setTemplateName(dagTemplateDTO.getName());
        dagOrderDTO.setTemplateId(dagTemplateDTO.getId());
        dagOrderDTO.setRegionId(opm.getRegionId());
        dagOrderDTO.setRegionName(opm.getRegionName());
        dagOrderDTO.setRegionCode(opm.getRegionCode());
        dagOrderDTO.setAzId(opm.getAzId());
        dagOrderDTO.setAzName(opm.getAzName());
        dagOrderDTO.setAzCode(opm.getAzCode());
        dagOrderDTO.setTenantId(opm.getTenantId());
        dagOrderDTO.setBillId(opm.getBillId());
        dagOrderDTO.setCreatedBy(opm.getCreatedBy());
        dagOrderDTO.setBusinessSystemId(opm.getBusinessSystemId());
        dagOrderDTO.setBusinessSystemName(opm.getBusinessSystemName());
        dagOrderDTO.setCreator(opm.getCreatedByName());
        dagOrderDTO.setStatus(ResOpenEnum.OPENING.getCode());
        return dagOrderDTO;
    }

    default void fillBase(DagOrderOpm dagOrderOpm, DagTemplateDTO dagTemplateDTO){
        //获取ecs对象
        List<CloudEcsResourceModel> ecsModelList = dagTemplateDTO.getEcsModelList();
        //获取mysql对象
        List<EcsModel> mysqlModelList = dagTemplateDTO.getMysqlModelList();
        //获取redis对象
        List<EcsModel> redisModelList = dagTemplateDTO.getRedisModelList();
        //获取gcs对象
        List<CpuEcsResourceModel> gcsModelList = dagTemplateDTO.getGcsModelList();
        //获取evs对象
        List<EvsModel> evsModelList = dagTemplateDTO.getEvsModelList();
        //获取eip对象
        List<EipModel> eipModelList = dagTemplateDTO.getEipModelList();
        //obs对象
        List<ObsModel> obsModelList = dagTemplateDTO.getObsModelList();
        //slb对象
        List<SlbModel> slbModelList = dagTemplateDTO.getSlbModelList();
        //nat对象
        List<NatGatwayModel> natModelList = dagTemplateDTO.getNatModelList();
        //vpc
        List<VpcModel> vpcModelList = dagTemplateDTO.getVpcModelList();

        opmBase(ecsModelList,dagOrderOpm);
        opmBase(mysqlModelList,dagOrderOpm);
        opmBase(redisModelList,dagOrderOpm);
        opmBase(gcsModelList,dagOrderOpm);
        opmBase(evsModelList,dagOrderOpm);
        opmBase(eipModelList,dagOrderOpm);
        opmBase(obsModelList,dagOrderOpm);
        opmBase(slbModelList,dagOrderOpm);
        opmBase(natModelList,dagOrderOpm);
        opmBase(vpcModelList,dagOrderOpm);

        dagOrderOpm.setEcsModelList(ecsModelList);
        dagOrderOpm.setMysqlModelList(mysqlModelList);
        dagOrderOpm.setRedisModelList(redisModelList);
        dagOrderOpm.setGcsModelList(gcsModelList);
        dagOrderOpm.setEvsModelList(evsModelList);
        dagOrderOpm.setEipModelList(eipModelList);
        dagOrderOpm.setObsModelList(obsModelList);
        dagOrderOpm.setSlbModelList(slbModelList);
        dagOrderOpm.setNatModelList(natModelList);
        dagOrderOpm.setVpcModelList(vpcModelList);
    }

    default <T extends BaseProductModel>void opmBase(List<T> modelList, DagOrderOpm dagOrderOpm) {
        if (ObjNullUtils.isNull(modelList)){
            return;
        }
        for (BaseProductModel model : modelList) {
            model.setRegionId(NumberUtil.parseLong(dagOrderOpm.getRegionId()));
            model.setRegionName(dagOrderOpm.getRegionName());
            model.setRegionCode(dagOrderOpm.getRegionCode());
            model.setAzId(NumberUtil.parseLong(dagOrderOpm.getAzId()));
            model.setAzCode(dagOrderOpm.getAzCode());
            model.setAzName(dagOrderOpm.getAzName());
            model.setTenantId(dagOrderOpm.getTenantId());
            model.setBillId(dagOrderOpm.getBillId());
            model.setBusinessSystemId(Long.valueOf(dagOrderOpm.getBusinessSystemId()));
            model.setBusinessSystemName(dagOrderOpm.getBusinessSystemName());
        }


    }


    default ProductGeneralCheckOpm convertCheck(DagOrderOpm dagOrderOpm) {
        ProductGeneralCheckOpm productGeneralCheckOpm = new ProductGeneralCheckOpm();
        //获取ecs对象
        List<CloudEcsResourceModel> ecsModelList = dagOrderOpm.getEcsModelList();
        //获取mysql对象
        List<EcsModel> mysqlModelList = dagOrderOpm.getMysqlModelList();
        //获取redis对象
        List<EcsModel> redisModelList = dagOrderOpm.getRedisModelList();
        //获取gcs对象
        List<CpuEcsResourceModel> gcsModelList = dagOrderOpm.getGcsModelList();
        //获取evs对象
        List<EvsModel> evsModelList = dagOrderOpm.getEvsModelList();
        //获取eip对象
        List<EipModel> eipModelList = dagOrderOpm.getEipModelList();
        //obs对象
        List<ObsModel> obsModelList = dagOrderOpm.getObsModelList();
        //slb对象
        List<SlbModel> slbModelList = dagOrderOpm.getSlbModelList();
        //nat对象
        List<NatGatwayModel> natModelList = dagOrderOpm.getNatModelList();
        productGeneralCheckOpm.setEcsModelList(ecsModelList);
        productGeneralCheckOpm.setMysqlModelList(mysqlModelList);
        productGeneralCheckOpm.setRedisModelList(redisModelList);
        productGeneralCheckOpm.setGcsModelList(gcsModelList);
        productGeneralCheckOpm.setEvsModelList(evsModelList);
        productGeneralCheckOpm.setEipModelList(eipModelList);
        productGeneralCheckOpm.setObsModelList(obsModelList);
        productGeneralCheckOpm.setSlbModelList(slbModelList);
        productGeneralCheckOpm.setNatModelList(natModelList);
        return productGeneralCheckOpm;
    }

}
