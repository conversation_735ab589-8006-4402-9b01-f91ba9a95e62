package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.DagTemplateMapper;
import com.datatech.slgzt.dao.model.DagTemplateDO;
import com.datatech.slgzt.model.query.DagTemplateQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * DAG模板 DAO接口
 */
@Repository
public class DagTemplateDAO {

    @Resource
    private DagTemplateMapper dagTemplateMapper;

    /**
     * 新增DAG模板
     */
    public void insert(DagTemplateDO dagTemplateDO) {
        dagTemplateMapper.insert(dagTemplateDO);
    }

    /**
     * 更新DAG模板
     */
    public void update(DagTemplateDO dagTemplateDO) {
        dagTemplateMapper.updateById(dagTemplateDO);
    }

    /**
     * 删除DAG模板
     */
    public void delete(String id) {
        dagTemplateMapper.deleteById(id);
    }

    /**
     * 获取DAG模板详情
     */
    public DagTemplateDO getById(String id) {
        return dagTemplateMapper.selectById(id);
    }

    /**
     * 根据名称查询DAG模板
     */
    public DagTemplateDO getByName(String name) {
        return dagTemplateMapper.selectOne(
            Wrappers.<DagTemplateDO>lambdaQuery()
                .eq(DagTemplateDO::getName, name)
        );
    }

    /**
     * 查询DAG模板列表
     */
    public List<DagTemplateDO> list(DagTemplateQuery query) {
        return dagTemplateMapper.selectList(
            Wrappers.<DagTemplateDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getName()), DagTemplateDO::getName, query.getName())
                .eq(ObjNullUtils.isNotNull(query.getCreatorId()), DagTemplateDO::getCreatorId, query.getCreatorId())
                .like(ObjNullUtils.isNotNull(query.getDescription()), DagTemplateDO::getDescription, query.getDescription())
                .orderByDesc(DagTemplateDO::getCreateTime)
        );
    }
} 