package com.datatech.slgzt.impl.service.dag;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.network.NetworkOrderMapper;
import com.datatech.slgzt.dao.mapper.network.NetworkSubnetOrderMapper;
import com.datatech.slgzt.dao.model.network.NetworkOrder;
import com.datatech.slgzt.dao.model.network.NetworkSubnetOrder;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.SourceTypeEnum;
import com.datatech.slgzt.manager.DagOrderManager;
import com.datatech.slgzt.model.dto.DagOrderDTO;
import com.datatech.slgzt.model.dto.DagProductDTO;
import com.datatech.slgzt.model.dto.OrderStatusNoticeDTO;
import com.datatech.slgzt.model.dto.network.NetworkCreateDTO;
import com.datatech.slgzt.model.dto.network.NetworkOrderDTO;
import com.datatech.slgzt.model.dto.network.SubnetDTO;
import com.datatech.slgzt.model.nostander.NetworkModel2;
import com.datatech.slgzt.model.nostander.SubnetModel;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.dag.DagResOpenService;
import com.datatech.slgzt.service.network.NetworkMessageService;
import com.datatech.slgzt.utils.Precondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月20日 11:30:42
 */
@Service
@Slf4j
public class DagResNetworkOpenServiceImpl implements DagResOpenService {

    @Resource
    private PlatformService platformService;

    @Resource
    private DagOrderManager dagOrderManager;


    @Resource
    private NetworkOrderMapper networkMapper;

    @Resource
    private NetworkMessageService networkService;

    @Resource
    private NetworkSubnetOrderMapper subnetMapper;

    @Override
    public Object openResource(DagProductDTO productDTO) {
        //不是只有ESC
        String productType = productDTO.getProductType();
        //开通资源类型要是GPS 或者是ESC
        Precondition.checkArgument(ProductTypeEnum.NETWORK.getCode().equals(productType), "非标资源不支持该类型开通");
        //把开通的网络资源挂载到对应的附加字段里去
        DagOrderDTO orderDTO = dagOrderManager.getById(productDTO.getOrderId());

        NetworkModel2 networkModel = JSON.parseObject(productDTO.getPropertySnapshot(), NetworkModel2.class);
        Long tenantId = platformService.getOrCreateTenantId(orderDTO.getBillId(), networkModel.getRegionCode());


        NetworkCreateDTO networkCreateDTO = new NetworkCreateDTO();
        networkCreateDTO.setRegionCode(orderDTO.getRegionCode());
        networkCreateDTO.setBillId(orderDTO.getBillId());
        networkCreateDTO.setBusinessSysId(Long.valueOf(orderDTO.getBusinessSystemId()));
        networkCreateDTO.setBusinessSysName(orderDTO.getBusinessSystemName());
        networkCreateDTO.setTenantId(tenantId);
//        networkCreateDTO.setTenantName("");
        networkCreateDTO.setOrderId(orderDTO.getId());
//        networkCreateDTO.setUserId();
        networkCreateDTO.setAzCode(orderDTO.getAzCode());
        networkCreateDTO.setFunctionalModule(networkModel.getFunctionalModule());
//        networkCreateDTO.setBottomTenantId(0L);
//        networkCreateDTO.setApplyUserId(0L);
//        networkCreateDTO.setApplyUserName("");
        networkCreateDTO.setSourceType(SourceTypeEnum.DAG.getPrefix());
//        networkCreateDTO.setModuleId(orderDTO.getModuleId());
//        networkCreateDTO.setModuleName(orderDTO.getModuleName());
//        networkCreateDTO.setOrderCode(orderDTO.getOrderCode());
//        networkCreateDTO.setCatalogueDomainCode(orderDTO.getCatalogueDomainCode());
//        networkCreateDTO.setCatalogueDomainName(orderDTO.getCatalogueDomainName());
//        networkCreateDTO.setDomainCode(orderDTO.getDomainCode());
//        networkCreateDTO.setDomainName(orderDTO.getDomainName());

        NetworkOrderDTO networkDTO = new NetworkOrderDTO();
        networkDTO.setNetworkId(networkModel.getNetworkId());
        networkDTO.setRegionCode(networkModel.getRegionCode());
        networkDTO.setName(networkModel.getName());
        networkDTO.setDescription(networkModel.getDescription());
        networkDTO.setOrderId(networkModel.getOrderId());
        networkDTO.setPlane(networkModel.getPlane());
        networkDTO.setTenantId(networkModel.getTenantId());
        networkDTO.setNetworkType(networkModel.getNetworkType());
        networkDTO.setSystemSource(networkModel.getSystemSource());
        networkDTO.setInstanceId(networkModel.getInstanceId());
        networkDTO.setVlanId(networkModel.getVlanId());
        networkDTO.setVlan(networkModel.getVlan());
        networkDTO.setFunctionalModule(networkModel.getFunctionalModule());
        networkDTO.setAzCode(networkModel.getAzCode());
        networkDTO.setCatalogueDomainCode(networkModel.getCatalogueDomainCode());
        networkDTO.setCatalogueDomainName(networkModel.getCatalogueDomainName());
        networkDTO.setDomainCode(networkModel.getDomainCode());
        networkDTO.setDomainName(networkModel.getDomainName());
        networkDTO.setDetail(networkModel.getDetail());


        List<SubnetDTO> subnetDTOList = new ArrayList<>();
        networkDTO.setSubnets(subnetDTOList);
        for (SubnetModel subnetModel : networkModel.getSubnets()) {
            SubnetDTO dto = new SubnetDTO();
            dto.setSubnetName(subnetModel.getSubnetName());
            dto.setCidr(subnetModel.getCidr());
//            dto.setDescription(subnetModel.getDescription());
            dto.setIpVersion(subnetModel.getIpVersion());
//            dto.setInstanceId(subnetModel.getInstanceId());
//            dto.setLevel2InstanceId(subnetModel.getLevel2InstanceId());
            dto.setUuid(subnetModel.getUuid());
            subnetDTOList.add(dto);
        }


        networkCreateDTO.setNetworks(Collections.singletonList(networkDTO));
//        networkCreateDTO.setVpcType(0);

//        //------------------产品参数设置结束-------------------------------------------------------
//        //把对应的产品都改成开通中状态
//        productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPENING.getCode());
//        productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        //------------------调用底层开通接口-------------------------------------------------------
        networkService.networkCreate(networkCreateDTO);
        Map<String, String> map = new HashMap<>();
        LambdaQueryWrapper<NetworkOrder> queryWrapper = Wrappers.lambdaQuery();
        // todo 这个order id不对，可能是其他单子创建的
        queryWrapper.eq(NetworkOrder::getOrderId, orderDTO.getId());
        for (NetworkOrder networkDO : networkMapper.selectList(queryWrapper)) {
            if (networkModel.getName().equals(networkDO.getName())
                    // todo 判断唯一性
                    && networkModel.getPlane().equals(networkDO.getPlane())) {
                List<NetworkSubnetOrder> subnetDOs = subnetMapper.selectByNetworkId(networkDO.getId());
                for (NetworkSubnetOrder subnetDO : subnetDOs) {
                    Optional<SubnetModel> subnetModelOptional = networkModel.getSubnets().stream().filter(i ->
                            i.getCidr().equals(subnetDO.getCidr())
                                    && i.getIpVersion().equals(subnetDO.getIpVersion())
                    ).findFirst();
                    subnetModelOptional.ifPresent(vpcSubnetModel ->
                            map.put(vpcSubnetModel.getDagId(), subnetDO.getId())
                    );
                }
            }
        }
        return map;
    }

    /**
     * 注册
     */
    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.NETWORK;
    }

    @Override
    public void layoutTaskNotify(OrderStatusNoticeDTO dto) {

    }
}
