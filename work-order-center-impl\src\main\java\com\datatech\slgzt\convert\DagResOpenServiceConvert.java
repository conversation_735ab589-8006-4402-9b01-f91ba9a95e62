package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.ActiveStatisticsDO;
import com.datatech.slgzt.model.dto.ActiveStatisticsDTO;
import com.datatech.slgzt.model.dto.vpc.VpcOrderDTO;
import com.datatech.slgzt.model.dto.vpc.VpcSubnetDTO;
import com.datatech.slgzt.model.nostander.VpcModel;
import com.datatech.slgzt.model.nostander.VpcSubnetModel;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 活跃统计转换器
 */
@Mapper(componentModel = "spring")
public interface DagResOpenServiceConvert {

    VpcModel vpcDto2Model(VpcOrderDTO dto);

    VpcOrderDTO vpcModel2dto(VpcModel model);

    VpcSubnetModel subnetDto2Model(VpcSubnetDTO dto);

    VpcSubnetDTO subnetModel2dto(VpcSubnetModel model);
}