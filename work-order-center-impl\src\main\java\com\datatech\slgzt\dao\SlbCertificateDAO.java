package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.SlbCertificateMapper;
import com.datatech.slgzt.dao.model.SlbCertificateDO;
import com.datatech.slgzt.model.dto.SlbCertificateDTO;
import com.datatech.slgzt.model.query.SlbCertificateQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * SLB证书DAO
 */
@Repository
public class SlbCertificateDAO {

    @Resource
    private SlbCertificateMapper slbCertificateMapper;
    
    /**
     * 插入
     */
    public void insert(SlbCertificateDO slbCertificateDO) {
        slbCertificateMapper.insert(slbCertificateDO);
    }
    
    /**
     * 更新
     */
    public void update(SlbCertificateDO slbCertificateDO) {
        slbCertificateMapper.updateById(slbCertificateDO);
    }
    
    /**
     * 删除
     */
    public void delete(String id) {
        slbCertificateMapper.deleteById(id);
    }
    
    /**
     * 根据ID查询
     */
    public SlbCertificateDO getById(String id) {
        return slbCertificateMapper.selectById(id);
    }

    /**
     * 根据任务ID 查询
     * @param createTaskId
     * @return
     */
    public List<SlbCertificateDO> listByCreateTaskId(String createTaskId){
        List<SlbCertificateDO> slbCertificateDOS = slbCertificateMapper.selectList(Wrappers.<SlbCertificateDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(createTaskId), SlbCertificateDO::getId, createTaskId));
        return slbCertificateDOS;
    }
    /**
     * 根据任务ID 查询
     * @param createResourceId
     * @return
     */
    public List<SlbCertificateDO> listByResourceId(String createResourceId){
        List<SlbCertificateDO> slbCertificateDOS = slbCertificateMapper.selectList(Wrappers.<SlbCertificateDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(createResourceId), SlbCertificateDO::getResourceId, createResourceId));
        return slbCertificateDOS;
    }

    /**
     * 查询列表
     */
    public List<SlbCertificateDO> list(SlbCertificateQuery query) {
        return slbCertificateMapper.selectList(Wrappers.<SlbCertificateDO>lambdaQuery()
                .like(ObjNullUtils.isNotNull(query.getCertificateName()), SlbCertificateDO::getCertificateName, query.getCertificateName())
                .eq(ObjNullUtils.isNotNull(query.getCertificateType()), SlbCertificateDO::getCertificateType, query.getCertificateType())
                .eq(ObjNullUtils.isNotNull(query.getDomainCode()), SlbCertificateDO::getDomainCode, query.getDomainCode())
                .eq(ObjNullUtils.isNotNull(query.getRegionId()), SlbCertificateDO::getRegionId, query.getRegionId())
                .eq(ObjNullUtils.isNotNull(query.getBusinessSystemId()), SlbCertificateDO::getBusinessSystemId, query.getBusinessSystemId())
                .eq(ObjNullUtils.isNotNull(query.getCatalogueDomainCode()), SlbCertificateDO::getCatalogueDomainCode, query.getCatalogueDomainCode())
                .ge(ObjNullUtils.isNotNull(query.getCreateTimeStart()), SlbCertificateDO::getCreateTime, query.getCreateTimeStart())
                .le(ObjNullUtils.isNotNull(query.getCreateTimeEnd()), SlbCertificateDO::getCreateTime, query.getCreateTimeEnd())
                .orderByDesc(SlbCertificateDO::getCreateTime));
    }


}