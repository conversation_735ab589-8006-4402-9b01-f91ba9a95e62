package com.datatech.slgzt.model.nostander;

import com.datatech.slgzt.model.BaseProductModel;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
public class VpcModel extends BaseProductModel {


   // private String id;


    private String vpcId;

    /**
     * 工单Id
     */
    private String orderId;

    /**
     * Vpc名称
     */
    private String vpcName;
    /**
     * vpc网络网段
     */
    // @NotBlank(message = "vpcIPv4网络网段不能为空")
    private String cidr;
    /**
     * vpcIPv6网络网段
     */
    private String ipv6Cidr;

    /**
     * Vpc描述
     */
    // todo 不需要
    private String description;

    /**
     * 子网，vpc可绑定多个子网
     */
    private List<VpcSubnetModel> subnetDTOList;


    private String detail;
    private String plane;

    // todo 不需要
    private String gid;
    /**
     * 租户id
     */
    // todo 不需要
    private Long tenantId;
    // todo 不需要
    private String tenantName;

    // todo 不需要
    private Long bottomTenantId;
    // todo 不需要
    private Integer vpcType;
}
