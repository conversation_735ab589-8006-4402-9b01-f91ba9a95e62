package com.datatech.slgzt.model.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月04日 14:50:52
 */
@Data
public class DagTemplateProductDTO {


    private String id;

    private String templateId;

    private String templateName;

    private List<String> domainCodeList;

    private List<String> catalogueDomainCodeList;

    private List<ReginModel> reginModelList;

    /**
     * 业务系统ID
     */
    private String businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    @Data
    public static class ReginModel{
        /**
         * 资源池id
         */
        private String regionId;

        /**
         * 资源池名称
         */
        private String regionName;

        /**
         * 资源池code
         */
        private String regionCode;
    }

}
