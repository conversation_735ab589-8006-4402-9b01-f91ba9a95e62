package com.datatech.slgzt.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * DAG产品DTO
 */
@Data
@Accessors(chain = true)
public class DagProductDTO {

    private Long id;

    private String dagId;

//    /**
//     * vpc id或者network id
//     * vpc id或者network id
//     * vpc id或者network id
//     */
//    private String vpcId;

    private String orderId;

    //产品类型
    private String productType;

    //产品创建状态
    private String status;

    //消息
    private String message;

    //属性快照
    private String propertySnapshot;

    //父类产品id 可以为空
    private Long parentProductId;

    /**
     * gid
     */
    private String gid;

    /**
     * gid
     */
    private Long subOrderId;

    private String openStatus;
} 