package com.datatech.slgzt.controller;

import com.datatech.slgzt.annotation.OperationLog;
import com.datatech.slgzt.convert.DagTemplateProductWebConvert;
import com.datatech.slgzt.manager.DagTemplateProductManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.DagTemplateProductDTO;
import com.datatech.slgzt.model.req.dag.DagTemplateProductCreateReq;
import com.datatech.slgzt.model.req.dag.DagTemplateProductPageReq;
import com.datatech.slgzt.model.req.dag.DagTemplateProductUpdateReq;
import com.datatech.slgzt.model.vo.dag.DagTemplateProductVO;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.warpper.PageWarppers;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * DAG模板产品控制器
 */
@RestController
@RequestMapping("/api/v1/dag/template/product")
public class DagTemplateProductController {

    @Resource
    private DagTemplateProductManager manager;

    @Resource
    private DagTemplateProductWebConvert convert;

    /**
     * 创建DAG模板产品
     */
    @PostMapping("/create")
    @OperationLog(description = "创建DAG模板产品", operationType = "CREATE")
    public CommonResult<Void> create(@RequestBody DagTemplateProductCreateReq req) {
        Precondition.checkArgument(StringUtils.isNotBlank(req.getTemplateId()), "模板ID不能为空");
        manager.insert(convert.req2dto(req));
        return CommonResult.success();
    }

    /**
     * 更新DAG模板产品
     */
    @PostMapping("/update")
    @OperationLog(description = "更新DAG模板产品", operationType = "UPDATE")
    public CommonResult<Void> update(@RequestBody DagTemplateProductUpdateReq req) {
        Precondition.checkArgument(StringUtils.isNotBlank(req.getId()), "ID不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getTemplateId()), "模板ID不能为空");
        manager.update(convert.req2dto(req));
        return CommonResult.success();
    }

    /**
     * 删除DAG模板产品
     */
    @PostMapping("/delete")
    @OperationLog(description = "删除DAG模板产品", operationType = "DELETE")
    public CommonResult<Void> delete(@RequestBody String id) {
        Precondition.checkArgument(StringUtils.isNotBlank(id), "ID不能为空");
        manager.delete(id);
        return CommonResult.success();
    }

    /**
     * 分页查询DAG模板产品
     */
    @PostMapping("/page")
    @OperationLog(description = "分页查询DAG模板产品", operationType = "QUERY")
    public CommonResult<PageResult<DagTemplateProductVO>> page(@RequestBody DagTemplateProductPageReq req) {
        PageResult<DagTemplateProductDTO> pageResult = manager.page(convert.req2query(req));
        return CommonResult.success(PageWarppers.box(pageResult, convert::dto2vo));
    }

} 