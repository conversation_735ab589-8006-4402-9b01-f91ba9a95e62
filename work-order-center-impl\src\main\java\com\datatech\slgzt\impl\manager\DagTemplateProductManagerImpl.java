package com.datatech.slgzt.impl.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datatech.slgzt.convert.DagTemplateProductManagerConvert;
import com.datatech.slgzt.dao.DagTemplateProductDAO;
import com.datatech.slgzt.dao.model.DagTemplateProductDO;
import com.datatech.slgzt.manager.DagTemplateProductManager;
import com.datatech.slgzt.model.dto.DagTemplateProductDTO;
import com.datatech.slgzt.model.query.DagTemplateProductQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * DAG模板产品Manager实现类
 */
@Service
public class DagTemplateProductManagerImpl implements DagTemplateProductManager {

    @Resource
    private DagTemplateProductDAO dao;

    @Resource
    private DagTemplateProductManagerConvert convert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insert(DagTemplateProductDTO dto) {
        dao.insert(convert.dto2do(dto));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(DagTemplateProductDTO dto) {
        dao.updateById(convert.dto2do(dto));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        dao.deleteById(id);
    }

    @Override
    public DagTemplateProductDTO getById(String id) {
        return convert.do2dto(dao.getById(id));
    }

    @Override
    public List<DagTemplateProductDTO> listByTemplateId(String templateId) {
        return StreamUtils.mapArray(dao.listByTemplateId(templateId), convert::do2dto);
    }

    @Override
    public List<DagTemplateProductDTO> list(DagTemplateProductQuery query) {
        return StreamUtils.mapArray(dao.list(query), convert::do2dto);
    }

    @Override
    public PageResult<DagTemplateProductDTO> page(DagTemplateProductQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<DagTemplateProductDO> list = dao.list(query);
        return PageWarppers.box(new PageInfo<>(list), convert::do2dto);
    }
} 