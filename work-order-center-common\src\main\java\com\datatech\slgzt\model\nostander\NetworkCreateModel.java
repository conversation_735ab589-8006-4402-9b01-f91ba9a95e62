package com.datatech.slgzt.model.nostander;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
public class NetworkCreateModel implements Serializable {

    @NotBlank(message = "云区域编码不能为空")
    private String regionCode;
    /**
     * 工单Id
     */
    @NotBlank(message = "工单ID不能为空")
    private String orderId;

    private String orderCode;

    private String functionalModule;

    // todo 固定为NFVO？？
    private String systemSource;

    private String azCode;

    private String catalogueDomainCode;

    private String catalogueDomainName;

    private String domainCode;

    private String domainName;

    /**
     * 租户id
     */
    // todo 不需要
    private Long bottomTenantId;

    // todo 不需要
    private String billId;

    private Long businessSysId;

    private String businessSysName;

    // todo 不需要
    private Long applyUserId;

    // todo 不需要
    private String applyUserName;

    private String sourceType;

    private Long moduleId;
    private String moduleName;

    /**
     * 租户id
     */
    private Long tenantId;
    /**
     *
     */
    // todo 不需要
    private String tenantName;

    /**
     * 用户id
     */
    // todo 不需要
    private Long userId;

    private List<NetworkModel2> networks;


}
