package com.datatech.slgzt.impl.service.dag;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.impl.service.standard.StandardEcsCombinationResOpenStrategyServiceProvider;
import com.datatech.slgzt.manager.DagOrderManager;
import com.datatech.slgzt.manager.DagProductManager;
import com.datatech.slgzt.manager.StandardWorkOrderManager;
import com.datatech.slgzt.manager.StandardWorkOrderProductManager;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.layout.ResOpenReqModel;
import com.datatech.slgzt.model.nostander.EipModel;
import com.datatech.slgzt.model.opm.ResOpenOpm;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.dag.DagResOpenService;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-03-14 10:52
 **/
@Service
@Slf4j
public class DagResEipOpenServiceImpl implements DagResOpenService {
    @Resource
    private DagProductManager productManager;

    @Resource
    private PlatformService platformService;

    @Resource
    private DagOrderManager dagOrderManager;

    @Value("${http.layoutCenterUrl}")
    private String layoutCenter;

    private final String layoutTaskInitUrl = "v1/erm/wokeOrderLayoutTaskInit_subscribe";

    @SneakyThrows
    @Override
    public Object openResource(DagProductDTO productDTO) {
        String productType = productDTO.getProductType();
        //开通资源类型要是G
        Precondition.checkArgument(ProductTypeEnum.EIP.getCode().equals(productType)
                || ProductTypeEnum.ECS.getCode().equals(productType), "算例编排开通资源类型不是eip");
        //把开通的网络资源挂载到对应的附加字段里去
        DagOrderDTO orderDTO = dagOrderManager.getById(productDTO.getOrderId());
        EipModel eipModel = JSON.parseObject(productDTO.getPropertySnapshot(), EipModel.class);
        eipModel.setGId(productDTO.getGid());
        Long tenantId = platformService.getOrCreateTenantId(orderDTO.getBillId(), eipModel.getRegionCode());
        //------------------基础参数设置----------------------------------------------------------
        ResOpenReqModel resOpenReqModel = new ResOpenReqModel();
        //--------------------基础部分设置----------------------------------------
        //设置计费号
        resOpenReqModel.setAccount(orderDTO.getBillId());
        //设置业务code;
        resOpenReqModel.setSourceExtType(OrderTypeEnum.SUBSCRIBE.getCode());
        //设置业务code
        resOpenReqModel.setBusinessCode("ECS_COMBINATION_SUBSCRIBE");
        //设置业务系统code
        resOpenReqModel.setBusinessSystemCode(orderDTO.getBusinessSystemId());
        //设置客户id
        resOpenReqModel.setCustomId(orderDTO.getCustomNo());
        //设置区域编码
        resOpenReqModel.setRegionCode(eipModel.getRegionCode());
        //设置的是主产品的SubOrderId 这里适配任务中心回调
        resOpenReqModel.setSubOrderId(productDTO.getSubOrderId());
        //设置租户id 可能是需要传入底层租户id 应该要查询下 目前不知道查询的方式
        resOpenReqModel.setTenantId(tenantId);
        //设置userId
        resOpenReqModel.setUserId(orderDTO.getCreatedBy());
        //设置来源固定3这个是给任务中心用的来判断回调的
        resOpenReqModel.setTaskSource(4);
        //开通资源
        List<ResOpenReqModel.ProductOrder> reqProductList = Lists.newArrayList();
        //------------设置EVS的参数----------------------------------------------------- todo 待确定参数是否满足
        List<ResOpenReqModel.ProductOrder> evsProduct = StandardEcsCombinationResOpenStrategyServiceProvider.INSTANCE.get(ProductTypeEnum.EIP)
                                                                                                                     .assembleParam(new ResOpenOpm().setGId(productDTO.getGid())
                        .setTenantId(tenantId)
                        .setSubOrderId(productDTO.getSubOrderId().toString())
                        .setEipModelList(Lists.newArrayList(eipModel))
                );
        reqProductList.addAll(evsProduct);
        resOpenReqModel.setProductOrders(reqProductList);
        //------------------产品参数设置结束-------------------------------------------------------
        //todo 把对应的产品都改成开通中状态,成功后下面状态修改开通中
        productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        //------------------调用底层开通接口-------------------------------------------------------
        log.info("算力编排资源开通，callLayoutOrder--调用编排中心初始化start--goodsId={},request url={}", JSON.toJSON(orderDTO.getId()), layoutCenter + layoutTaskInitUrl);
        Mapper dataMapper = OkHttps.sync(layoutCenter + layoutTaskInitUrl)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(resOpenReqModel))
                .post()
                .getBody()
                .toMapper();
        String success = dataMapper.getString("success");
        Precondition.checkArgument("1".equals(success), "资源开通失败，callLayoutOrder--编排中心初始化返回结果失败");
        log.info("算力编排资源开通，callLayoutOrder--调用编排中心初始化end--goodsId={},response:{}", JSON.toJSON(orderDTO.getId()), JSON.toJSON(dataMapper));
        return null;
    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.EIP;
    }

    @Override
    public void layoutTaskNotify(OrderStatusNoticeDTO dto) {

    }

    /**
     * 查询obs服务是否存在
     * @return
     */

}
