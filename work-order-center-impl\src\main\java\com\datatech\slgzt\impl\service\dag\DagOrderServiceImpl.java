package com.datatech.slgzt.impl.service.dag;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.consumer.BatchRestartConsumer;
import com.datatech.slgzt.convert.DagOrderServiceConvert;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.manager.DagOrderManager;
import com.datatech.slgzt.manager.DagProductManager;
import com.datatech.slgzt.manager.DagTemplateManager;
import com.datatech.slgzt.model.BatchRestartModel;
import com.datatech.slgzt.model.KafkaMessage;
import com.datatech.slgzt.model.dto.DagOrderDTO;
import com.datatech.slgzt.model.dto.DagProductDTO;
import com.datatech.slgzt.model.dto.DagTemplateDTO;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.opm.DagOrderOpm;
import com.datatech.slgzt.model.opm.ProductGeneralCheckOpm;
import com.datatech.slgzt.service.DagOrderService;
import com.datatech.slgzt.service.ProductGeneralCheckService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.utils.UuidUtil;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.configuration.JobRegistry;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.Future;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月03日 15:48:40
 */
@Service
public class DagOrderServiceImpl implements DagOrderService {

    @Resource
    private DagOrderManager dagOrderManager;

    @Resource
    private DagProductManager productManager;

    @Resource
    private DagTemplateManager dagTemplateManager;


    @Resource
    private JobLauncher jobLauncher;

    @Resource
    private JobRegistry jobRegistry;

    @Resource
    private ProductGeneralCheckService productGeneralCheckService;

    @Resource
    private KafkaTemplate<String, Object> kafkaTemplate;


    @Resource
    private DagOrderServiceConvert convert;

    @SneakyThrows
    @Override
    public void open(DagOrderOpm opm) {
        DagTemplateDTO dagTemplateDTO = dagTemplateManager.getById(opm.getTemplateId());
        convert.fillBase(opm,dagTemplateDTO);
        //校验
        ProductGeneralCheckOpm checkOpm = convert.convertCheck(opm);
        productGeneralCheckService.checkImageExist(checkOpm);
        productGeneralCheckService.checkFlavorExist(checkOpm);
        productGeneralCheckService.checkProductInResourcePool(checkOpm);
        productGeneralCheckService.checkProductInResourcePoolCapacity(checkOpm);
        Precondition.checkArgument(ObjNullUtils.isNull(checkOpm.getErrorMessages()),checkOpm.getErrorMessages().toString());
        //创建订单
        String orderId = dagOrderManager.add(convert.opm2Order(opm, dagTemplateDTO));
        //创建产品
        //-------------------------vpc----------------------------------------
        StreamUtils.mapArray(opm.getVpcModelList(), Function.identity()).forEach(item -> {
            checkFillVpcResource(item, orderId);
        });
        //-----------------------ecs----------------------------------------
        //校验填充资源
        StreamUtils.mapArray(opm.getEcsModelList(), Function.identity()).forEach(item -> {
            checkFillEscResource(item, orderId);
        });
        //-----------------------gcs----------------------------------------
        //校验填充资源
        StreamUtils.mapArray(opm.getGcsModelList(), Function.identity()).forEach(item -> {
            checkFillEscResource(item, orderId);
        });
        //-----------------------mysql----------------------------------------
        //校验填充资源
        StreamUtils.mapArray(opm.getMysqlModelList(), Function.identity()).forEach(item -> {
            checkFillEscResource(item, orderId);
        });
        //-----------------------redis----------------------------------------
        //校验填充资源
        StreamUtils.mapArray(opm.getRedisModelList(), Function.identity()).forEach(item -> {
            checkFillEscResource(item, orderId);
        });
        //-----------------------nat----------------------------------------
        StreamUtils.mapArray(opm.getNatModelList(), Function.identity()).forEach(item -> {
            checkFillNatResource(item, orderId);
        });
        //-----------------------slb----------------------------------------
        StreamUtils.mapArray(opm.getSlbModelList(), Function.identity()).forEach(item -> {
            checkFillSlbResource(item, orderId);
        });
        //-----------------------evs----------------------------------------
        StreamUtils.mapArray(opm.getEvsModelList(), Function.identity()).forEach(item -> {
            checkFillEvsResource(item, orderId);
        });
        //-----------------------eip----------------------------------------
        StreamUtils.mapArray(opm.getEipModelList(), Function.identity()).forEach(item -> {
            checkFillEipResource(item, orderId);
        });
        //-----------------------obs----------------------------------------
        StreamUtils.mapArray(opm.getObsModelList(), Function.identity()).forEach(item -> {
            checkFillObsResource(item, orderId);
        });
        //开启流程
        Future<String> futureJobId = startProcess(orderId);
        String jobId = futureJobId.get();
        //存入jobId
        DagOrderDTO dagOrderDTO = dagOrderManager.getById(orderId);
        dagOrderDTO.setJobId(jobId);
        dagOrderManager.update(dagOrderDTO);
        //重启流程
        kafkaTemplate.send(BatchRestartConsumer.BATCH_RESTART,orderId,KafkaMessage.of(new BatchRestartModel()
        .setJobId(jobId)));
    }



    @SneakyThrows
    @Async
    public AsyncResult<String> startProcess(String workOrderId) {
        JobParameters jobParameters = new JobParametersBuilder()
                .addString("orderId", workOrderId)
                .addLong("time", System.currentTimeMillis())
                .toJobParameters();
        //第一次同步执行必然会失败的 主要是为了返回id
        String jobId= jobLauncher.run(jobRegistry.getJob("dagProductCreateJob"), jobParameters)
                                                .getId()
                                                .toString();
        return new AsyncResult<>(jobId);
    }


    private void checkFillObsResource(ObsModel model, String workOrderId) {
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getObsName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品
            model.setOriginName(originalName);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setObsName(originalName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            String productType = model.getProductType();
            model.setId(mainId);
            DagProductDTO product = new DagProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.OBS.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }

    private void checkFillEipResource(EipModel model, String workOrderId) {
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getEipName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            model.setOriginName(originalName);
            // 创建主产品
            String productType = model.getProductType();
            model.setId(mainId);
            DagProductDTO product = new DagProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.EIP.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }


    private void checkFillEvsResource(EvsModel model, String workOrderId) {
        //并且大于0
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品
            String productType = model.getProductType();
            model.setId(mainId);
            DagProductDTO product = new DagProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.EVS.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }



    private void checkFillSlbResource(SlbModel model, String workOrderId) {
        Precondition.checkArgument(model.getSlbName(), "slbName不能为空");
        //如果绑定公网IP 需要校验公网IP的内容
        if (model.getBindPublicIp()) {
            List<EipModel> eipModelList = model.getEipModelList();
            Precondition.checkArgument(eipModelList, "eipModelList不能为空");
            for (EipModel eipModel : eipModelList) {
                Precondition.checkArgument(eipModel.getBandwidth(), "bandwidth不能为空");
                eipModel.setAzCode(model.getAzCode());
                eipModel.setRegionCode(model.getRegionCode());
            }
        } else {
            model.setEipModelList(null);
        }
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0, "openNum必须大于0");
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getSlbName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            model.setOriginName(originalName);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setSlbName(originalName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            // 创建主产品
            String productType = model.getProductType();
            model.setId(mainId);
            DagProductDTO product = new DagProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.SLB.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
            if (model.getBindPublicIp()) {
                createEipProduct(model.getEipModelList(), mainId, workOrderId);
            }
        }
    }


    private void checkFillNatResource(NatGatwayModel model, String workOrderId) {
        if (model.getBindPublicIp()) {
            List<EipModel> eipModelList = model.getEipModelList();
            for (EipModel eipModel : eipModelList) {
                eipModel.setAzCode(model.getAzCode());
                eipModel.setRegionCode(model.getRegionCode());
            }
        } else {
            model.setEipModelList(null);
        }
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0, "openNum必须大于0");
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getNatName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            model.setOriginName(originalName);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setNatName(originalName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            // 创建主产品
            String productType = model.getProductType();
            model.setId(mainId);
            DagProductDTO product = new DagProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.NAT.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
            if (model.getBindPublicIp()) {
                createEipProduct(model.getEipModelList(), mainId, workOrderId);
            }
        }
    }

    private void checkFillEscResource(EcsModel model, String workOrderId) {
        //如果存在挂载数据盘 需要校验挂载数据盘的内容
        if (model.getMountDataDisk()) {
            List<EvsModel> mountDataDiskList = model.getMountDataDiskList();
            for (EvsModel evsModel : mountDataDiskList) {
                evsModel.setAzCode(model.getAzCode());
                evsModel.setRegionCode(model.getRegionCode());
            }
        } else {
            model.setMountDataDiskList(null);
        }
        //如果绑定公网IP 需要校验公网IP的内容
        if (model.getBindPublicIp()) {
            List<EipModel> eipModelList = model.getEipModelList();
            for (EipModel eipModel : eipModelList) {
                eipModel.setAzCode(model.getAzCode());
                eipModel.setRegionCode(model.getRegionCode());
            }
        } else {
            model.setEipModelList(null);
        }
        String originalVmName = model.getVmName(); // 保存原始名称
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品（ESC）
            String productType = model.getProductType();
            model.setMainIds(mainIds);
            model.setId(mainId);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setOriginName(originalVmName);
            model.setVmName(originalVmName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            DagProductDTO product = new DagProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(productType));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
            // 创建关联子产品 如果不是ESC和GCS不用创建子产品
            // 创建SSD子产品
            createEvsProduct(model.getMountDataDiskList(), mainId, workOrderId);
            // 创建EIP子产品
            createEipProduct(model.getEipModelList(), mainId, workOrderId);
        }
    }


    private void checkFillVpcResource(VpcModel model, String workOrderId) {
        //并且大于0
        List<Long> mainIds = generateMainIds(1);
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品
            String productType = model.getProductType();
            model.setId(mainId);
            DagProductDTO product = new DagProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.EVS.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }



    private void createEvsProduct(List<EvsModel> models, Long parentId, String workOrderId) {
        if (models == null) return;
        models.stream().map(model -> {
            long id = IdUtil.getSnowflake().nextId();
            model.setId(id);
            DagProductDTO product = new DagProductDTO();
            product.setId(id);
            product.setProductType(ProductTypeEnum.EVS.getCode());
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setPropertySnapshot(JSON.toJSONString(model));
            product.setParentProductId(parentId);
            product.setGid(UuidUtil.getGid(ProductTypeEnum.EVS.getCode()));
            return product;
        }).forEach(productManager::insert);
    }

    private void createEipProduct(List<EipModel> models, Long parentId, String workOrderId) {
        if (models == null) return;
        models.stream().map(model -> {
            long id = IdUtil.getSnowflake().nextId();
            model.setId(id);
            DagProductDTO product = new DagProductDTO();
            product.setId(id);
            product.setProductType(ProductTypeEnum.EIP.getCode());
            product.setOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setPropertySnapshot(JSON.toJSONString(model));
            product.setParentProductId(parentId);
            product.setGid(UuidUtil.getGid(ProductTypeEnum.EIP.getCode()));
            return product;
        }).forEach(productManager::insert);
    }
    private List<Long> generateMainIds(int openNum) {
        List<Long> mainIds = Lists.newArrayListWithCapacity(openNum);
        for (int i = 0; i < openNum; i++) {
            mainIds.add(IdUtil.getSnowflake().nextId());
        }
        return mainIds;
    }
}
