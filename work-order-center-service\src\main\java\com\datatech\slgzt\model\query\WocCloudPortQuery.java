package com.datatech.slgzt.model.query;


import lombok.Data;

import java.time.LocalDateTime;

@Data
public class WocCloudPortQuery {

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 云端口名称
     */
    private String cloudPortName;


    private String businessSystemName;
    /**
     * 域名
     */
    private String domainCode;


    /**
     * 业务系统ID
     */
    private String  businessSystemId;

    /**
     * 资源池Id
     */
    private String regionId;


    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;
}
