package com.datatech.slgzt.convert;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.dao.model.DagTemplateDO;
import com.datatech.slgzt.model.dto.DagTemplateDTO;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

/**
 * DAG模板对象转换器
 */
@Mapper(componentModel = "spring")
public interface DagTemplateManagerConvert {
    
    /**
     * DTO转DO
     */
    default DagTemplateDO dto2do(DagTemplateDTO dto){
        if (ObjNullUtils.isNull(dto)){
            return null;
        }
        DagTemplateDO entity = new DagTemplateDO();
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        entity.setCreator(dto.getCreator());
        entity.setCreatorId(dto.getCreatorId());
        entity.setDescription(dto.getDescription());
        entity.setDagCanvas(dto.getDagCanvas());
        entity.setVpcModelListStr(list2Json(dto.getVpcModelList()));
        entity.setEcsModelListStr(list2Json(dto.getEcsModelList()));
        entity.setMysqlModelListStr(list2Json(dto.getMysqlModelList()));
        entity.setRedisModelListStr(list2Json(dto.getRedisModelList()));
        entity.setGcsModelListStr(list2Json(dto.getGcsModelList()));
        entity.setEvsModelListStr(list2Json(dto.getEvsModelList()));
        entity.setEipModelListStr(list2Json(dto.getEipModelList()));
        entity.setNatModelListStr(list2Json(dto.getNatModelList()));
        entity.setSlbModelListStr(list2Json(dto.getSlbModelList()));
        entity.setObsModelListStr(list2Json(dto.getObsModelList()));
        return entity;

    }

    /**
     * DO转DTO
     */
    default DagTemplateDTO do2dto(DagTemplateDO entity){
        if (ObjNullUtils.isNull(entity)){
            return null;
        }
        DagTemplateDTO dto = new DagTemplateDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setCreator(entity.getCreator());
        dto.setCreatorId(entity.getCreatorId());
        dto.setDescription(entity.getDescription());
        dto.setDagCanvas(entity.getDagCanvas());
        dto.setVpcModelList(jsonToVpc(entity.getVpcModelListStr()));
        dto.setEcsModelList(jsonToEcs(entity.getEcsModelListStr()));
        dto.setMysqlModelList(jsonToMysql(entity.getMysqlModelListStr()));
        dto.setRedisModelList(jsonToRedis(entity.getRedisModelListStr()));
        dto.setGcsModelList(jsonToGcs(entity.getGcsModelListStr()));
        dto.setEvsModelList(jsonToEvs(entity.getEvsModelListStr()));
        dto.setEipModelList(jsonToEip(entity.getEipModelListStr()));
        dto.setNatModelList(jsonToNat(entity.getNatModelListStr()));
        dto.setSlbModelList(jsonToSlb(entity.getSlbModelListStr()));
        dto.setObsModelList(jsonToObs(entity.getObsModelListStr()));
        return dto;
    }


    @Named("list2Json")
    default String list2Json(List list) {
        if (ObjNullUtils.isNull(list)) {
            return "";
        } else {
            return JSON.toJSONString(list);
        }
    }


    @Named("jsonToEcs")
    default List<CloudEcsResourceModel> jsonToEcs(String json) {
        if (ObjNullUtils.isNull(json)) {
            return null;
        } else {
            return JSON.parseArray(json, CloudEcsResourceModel.class);
        }
    }

    @Named("jsonToVpc")
    default List<VpcModel> jsonToVpc(String json) {
        if (ObjNullUtils.isNull(json)) {
            return null;
        } else {
            return JSON.parseArray(json, VpcModel.class);
        }
    }

    @Named("jsonToMysql")
    default List<EcsModel> jsonToMysql(String json) {
        if (ObjNullUtils.isNull(json)) {
            return null;
        } else {
            return JSON.parseArray(json, EcsModel.class);
        }
    }

    @Named("jsonToRedis")
    default List<EcsModel> jsonToRedis(String json) {
        if (ObjNullUtils.isNull(json)) {
            return null;
        } else {
            return JSON.parseArray(json, EcsModel.class);
        }
    }

    @Named("jsonToGcs")
    default List<CpuEcsResourceModel> jsonToGcs(String json) {
        if (ObjNullUtils.isNull(json)) {
            return null;
        } else {
            return JSON.parseArray(json, CpuEcsResourceModel.class);
        }
    }

    @Named("jsonToEvs")
    default List<EvsModel> jsonToEvs(String json) {
        if (ObjNullUtils.isNull(json)) {
            return null;
        } else {
            return JSON.parseArray(json, EvsModel.class);
        }
    }

    @Named("jsonToEip")
    default List<EipModel> jsonToEip(String json) {
        if (ObjNullUtils.isNull(json)) {
            return null;
        } else {
            return JSON.parseArray(json, EipModel.class);
        }
    }

    @Named("jsonToNat")
    default List<NatGatwayModel> jsonToNat(String json) {
        if (ObjNullUtils.isNull(json)) {
            return null;
        } else {
            return JSON.parseArray(json, NatGatwayModel.class);
        }
    }

    @Named("jsonToSlb")
    default List<SlbModel> jsonToSlb(String json) {
        if (ObjNullUtils.isNull(json)) {
            return null;
        } else {
            return JSON.parseArray(json, SlbModel.class);
        }
    }

    @Named("jsonToObs")
    default List<ObsModel> jsonToObs(String json) {
        if (ObjNullUtils.isNull(json)) {
            return null;
        } else {
            return JSON.parseArray(json, ObsModel.class);
        }
    }
} 