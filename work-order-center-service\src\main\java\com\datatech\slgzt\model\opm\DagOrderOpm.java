package com.datatech.slgzt.model.opm;

import com.datatech.slgzt.model.nostander.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月03日 15:46:59
 */
@Data
public class DagOrderOpm {


    private String domainCode;

    private String domainName;

    private String catalogueDomainCode;

    private String catalogueDomainName;

    /**
     * 模版ID
     */
    private String templateId;

    /**
     * 资源池id
     */
    private String regionId;

    /**
     * 资源池名称
     */
    private String regionName;

    /**
     * 资源池code
     */
    private String regionCode;

    /**
     * azId
     */
    private String azId;

    /**
     * az名称
     */
    private String azCode;

    /**
     * az名称
     */
    private String azName;


    /**
     * 业务系统ID
     */
    private String businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 模块ID
     */
    private Long moduleId;

    /**
     * 模块名称
     */
    private String moduleName;
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 计费号
     */
    private String billId;


    //----------------------------------------------------------------------

    private List<VpcModel> vpcModelList;

    /**
     * Ecs申请资源列表的json
     *
     */
    private List<CloudEcsResourceModel> ecsModelList;

    /**
     * mysql申请资源列表的json
     *
     */
    private List<EcsModel> mysqlModelList;

    private List<EcsModel> redisModelList;

    /**
     * Cpu申请资源列表的json,gcs
     */
    private List<CpuEcsResourceModel> gcsModelList;

    /**
     * evs申请资源列表的json
     */
    private List<EvsModel> evsModelList;

    /**
     * eip申请资源列表的json
     */
    private List<EipModel> eipModelList;

    /**
     * nat资源申请json
     */
    private List<NatGatwayModel> natModelList;

    /**
     * slb资源申请json
     */
    private List<SlbModel> slbModelList;

    /**
     * obs资源申请json
     */
    private List<ObsModel> obsModelList;

    /**
     * 创建人
     */
    private Long createdBy;

    /**
     * 创建人名称
     */
    private String createdByName;

}
