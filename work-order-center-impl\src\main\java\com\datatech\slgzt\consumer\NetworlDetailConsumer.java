package com.datatech.slgzt.consumer;

import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.ServiceTypeEnum;
import com.datatech.slgzt.enums.network.NetworkStatusEnum;
import com.datatech.slgzt.model.vo.callback.TaskVO;
import com.datatech.slgzt.service.network.NetworkCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025年 03月14日 15:20:59
 */
@Slf4j
@Service
public class NetworlDetailConsumer {

    @Resource
    private NetworkCommonService networkCommonService;

    /**
     * vpc资源信息详情topic
     */
    private final static String WORK_ORDER_VPC_TOPIC = "oac_resource_vpc_detail_topic";


    @KafkaListener(groupId = "work-order-resource-vpc-group", topics = {WORK_ORDER_VPC_TOPIC})
    public void consumeVpcMessage(List<ConsumerRecord<String, String>> consumerRecordList) {
        log.info("network监听任务消息: {}", consumerRecordList.size());
        for (ConsumerRecord<String, String> record : consumerRecordList) {
            TaskVO taskVO = JSONObject.parseObject(record.value(), TaskVO.class);
            String resourceType = taskVO.getResourceType();
            log.info("network监听任务消息 taskVO: {}", JSONObject.toJSONString(taskVO));
            if (NetworkStatusEnum.EXECUTING.getType().equals(taskVO.getStatus())) {
                continue;
            }
            if (ServiceTypeEnum.VPC.getDesc().equals(resourceType)) {
                networkCommonService.vpc(taskVO);
            } else if (ServiceTypeEnum.NETWORK.getDesc().equals(resourceType)) {
                networkCommonService.network(taskVO);
            } else if (ServiceTypeEnum.SUBNET.getDesc().equals(resourceType)) {
                networkCommonService.subnet(taskVO);
            }
        }
    }

}
