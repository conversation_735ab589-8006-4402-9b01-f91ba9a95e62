package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * DAG模板产品 DO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("WOC_DAG_TEMPLATE_PRODUCT")
public class DagTemplateProductDO extends BaseDO {

    @TableField("ID")
    private String id;

    @TableField("TEMPLATE_ID")
    private String templateId;

    @TableField("TEMPLATE_NAME")
    private String templateName;

    @TableField("DOMAIN_CODE_LIST")
    private String domainCodeList;

    @TableField("CATALOGUE_DOMAIN_CODE_LIST")
    private String catalogueDomainCodeList;

    @TableField("REGIN_MODEL_LIST")
    private String reginModelList;

    @TableField("BUSINESS_SYSTEM_ID")
    private String businessSystemId;

    @TableField("BUSINESS_SYSTEM_NAME")
    private String businessSystemName;
} 