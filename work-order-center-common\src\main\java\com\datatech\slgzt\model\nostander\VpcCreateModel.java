package com.datatech.slgzt.model.nostander;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class VpcCreateModel {
    @NotBlank(message = "云区域编码不能为空")
    private String regionCode;


    // todo 不需要
    private String billId;

    private Long businessSysId;

    private String businessSysName;

    /**
     * 租户id
     */
    private Long tenantId;
    /**
     *
     */
    // todo 不需要
    private String tenantName;

    /**
     * 工单Id
     */
    private String orderId;

    /**
     * 用户id
     */
    // todo 不需要
    private Long userId;

    /**
     * 子网可用区编码
     */
    private String azCode;

    private String functionalModule;

    // todo 不需要
    private Long bottomTenantId;

    // todo 不需要
    private Long applyUserId;

    // todo 不需要
    private String applyUserName;

    private String sourceType;

    private Long moduleId;
    private String moduleName;
    private String orderCode;

    private String catalogueDomainCode;

    private String catalogueDomainName;

    private String domainCode;

    private String domainName;

    private List<VpcModel> networks;

    // todo 不需要
    private Integer vpcType;
}
