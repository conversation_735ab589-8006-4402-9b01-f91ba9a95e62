package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.cloudPort.WocCloudPortMapper;
import com.datatech.slgzt.dao.model.cloudPort.WocCloudPortDO;
import com.datatech.slgzt.model.query.WocCloudPortQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月31日 9:23:20
 */
@Repository
public class WocCloudPortDAO {

    @Resource
    private WocCloudPortMapper cloudPortMappermapper;

    /**
     * 插入
     */
    public void insert(WocCloudPortDO wocCloudPortDO) {
        cloudPortMappermapper.insert(wocCloudPortDO);
    }

    /**
     * 更新
     */
    public void update(WocCloudPortDO wocCloudPortDO) {
        cloudPortMappermapper.updateById(wocCloudPortDO);
    }

    /**
     * 删除
     */
    public void delete(String id) {
        cloudPortMappermapper.deleteById(id);
    }

    /**
     * 根据ID查询
     */
    public WocCloudPortDO getById(String id) {
        return cloudPortMappermapper.selectById(id);
    }

    /**
     * 根据任务ID 查询
     * @param createTaskId
     * @return
     */
    public List<WocCloudPortDO> listByCreateTaskId(String createTaskId){
        List<WocCloudPortDO> WocCloudPortDOs = cloudPortMappermapper.selectList(Wrappers.<WocCloudPortDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(createTaskId), WocCloudPortDO::getId, createTaskId));
        return WocCloudPortDOs;
    }


    /**
     * 查询列表
     */
    public List<WocCloudPortDO> list(WocCloudPortQuery query) {
        return cloudPortMappermapper.selectList(Wrappers.<WocCloudPortDO>lambdaQuery()
                .like(ObjNullUtils.isNotNull(query.getCloudPortName()), WocCloudPortDO::getCloudPortName, query.getCloudPortName())
                .eq(ObjNullUtils.isNotNull(query.getBusinessSystemId()), WocCloudPortDO::getBusinessSystemId, query.getBusinessSystemId())
                .ge(ObjNullUtils.isNotNull(query.getCreateTimeStart()), WocCloudPortDO::getCreateTime, query.getCreateTimeStart())
                .le(ObjNullUtils.isNotNull(query.getCreateTimeEnd()), WocCloudPortDO::getCreateTime, query.getCreateTimeEnd())
                .orderByDesc(WocCloudPortDO::getCreateTime));
    }

}
