package com.datatech.slgzt.model.req.slb;

import com.datatech.slgzt.utils.Precondition;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * SLB证书新增请求
 */
@Data
public class SlbCertificateCreateReq {
    
    /**
     * 证书名称
     */
    private String certificateName;

    /**
     * 公钥内容
     */
    private String publicKeyContent;

    /**
     * 私钥内容
     */
    private String privateKeyContent;

    /**
     * 证书类型
     */
    private String certificateType;

    /**
     * 域名编码
     */
    private String domainCode;

    /**
     * 域名名称
     */
    private String domainName;

    /**
     * 资源池编码编码
     */
    private String regionCode;

    /**
     * 资源池编码编码
     */
    private String regionName;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 业务系统id
     */
    private String  businessSystemId;

    /**
     * 云类型
     */
    private String catalogueDomainCode;



} 