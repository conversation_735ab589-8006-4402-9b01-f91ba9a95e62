package com.datatech.slgzt.model.vo.dag;

import com.datatech.slgzt.model.nostander.*;
import lombok.Data;

import java.util.List;

/**
 * DAG模板视图对象
 */
@Data
public class DagTemplateVO {
    /**
     * 模板ID
     */
    private String id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板描述
     */
    private String description;

    /**
     * VPC资源列表
     */
    private List<VpcModel> vpcModelList;

    /**
     * ECS资源列表
     */
    private List<EcsModel> ecsModelList;

    /**
     * MySQL资源列表
     */
    private List<EcsModel> mysqlModelList;

    /**
     * Redis资源列表
     */
    private List<EcsModel> redisModelList;

    /**
     * GCS资源列表
     */
    private List<EcsModel> gcsModelList;

    /**
     * EVS资源列表
     */
    private List<EvsModel> evsModelList;

    /**
     * EIP资源列表
     */
    private List<EipModel> eipModelList;

    /**
     * NAT网关资源列表
     */
    private List<NatGatwayModel> natModelList;

    /**
     * SLB资源列表
     */
    private List<SlbModel> slbModelList;

    /**
     * OBS资源列表
     */
    private List<ObsModel> obsModelList;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建人ID
     */
    private String creatorId;
} 