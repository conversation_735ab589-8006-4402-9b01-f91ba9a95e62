package com.datatech.slgzt.model.vo.region;

import lombok.Data;

import java.util.List;

/**
 * 资源池树形结构VO
 */
@Data
public class RegionTreeVO {
    /**
     * 云类型名称
     */
    private String catalogueName;

    /**
     * 云类型编码
     */
    private String catalogueCode;

    /**
     * 云平台列表
     */
    private List<CloudPlatformVO> platforms;

    @Data
    public static class CloudPlatformVO {
        /**
         * 云平台名称
         */
        private String domainName;

        /**
         * 云平台编码
         */
        private String domainCode;

        /**
         * 资源池列表
         */
        private List<RegionVO> regions;
    }
} 