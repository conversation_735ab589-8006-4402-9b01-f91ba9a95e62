package com.datatech.slgzt.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * DAG工单 DTO
 */
@Data
public class DagOrderDTO {
    /**
     * 主键ID
     */
    private String id;

    private String orderCode;

    /**
     * domainCode
     */
    private String domainCode;

    private String domainName;


    private String catalogueDomainCode;

    private String catalogueDomainName;

    /**
     * 模版名称
     */
    private String templateName;

    /**
     * 模版ID
     */
    private String templateId;

    /**
     * 资源池id
     */
    private String regionId;

    /**
     * 资源池名称
     */
    private String regionName;

    /**
     * 资源池code
     */
    private String regionCode;

    /**
     * azId
     */
    private String azId;

    /**
     * az名称
     */
    private String azCode;

    /**
     * az名称
     */
    private String azName;

    /**
     * DAG状态
     */
    private String status;

    /**
     * 业务系统ID
     */
    private String businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 模块ID
     */
    private Long moduleId;

    /**
     * 所属业务模块
     */
    private String moduleName;
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 计费号
     */
    private String billId;

    /**
     * 集团编号
     */
    private String customNo;

    /**
     * 创建人
     */
    private Long createdBy;

    /**
     * 创建人名称
     */
    private String creator;

    private String jobId;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;



} 