package com.datatech.slgzt.controller;

import com.datatech.slgzt.convert.DagTemplateWebConvert;
import com.datatech.slgzt.manager.DagTemplateManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.DagTemplateDTO;
import com.datatech.slgzt.model.query.DagTemplateQuery;
import com.datatech.slgzt.model.req.dag.DagTemplateCreateReq;
import com.datatech.slgzt.model.req.dag.DagTemplateGetReq;
import com.datatech.slgzt.model.req.dag.DagTemplatePageReq;
import com.datatech.slgzt.model.req.dag.DagTemplateUpdateReq;
import com.datatech.slgzt.model.vo.dag.DagTemplateVO;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.warpper.PageWarppers;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * DAG模板控制器
 */
@RestController
@RequestMapping("/dagTemplate")
public class DagTemplateController {

    @Resource
    private DagTemplateManager dagTemplateManager;

    @Resource
    private DagTemplateWebConvert dagTemplateWebConvert;

    /**
     * 创建DAG模板
     */
    @PostMapping("/create")
    public CommonResult<Void> create(@Valid @RequestBody DagTemplateCreateReq req) {
        DagTemplateDTO dto = dagTemplateWebConvert.req2dto(req);
        dagTemplateManager.create(dto);
        return CommonResult.success();
    }

    /**
     * 更新DAG模板
     */
    @PostMapping("/update")
    public CommonResult<Void> update(@Valid @RequestBody DagTemplateUpdateReq req) {
        DagTemplateDTO dto = dagTemplateWebConvert.req2dto(req);
        dagTemplateManager.update(dto);
        return CommonResult.success();
    }

    /**
     * 删除DAG模板
     */
    @PostMapping("/delete")
    public CommonResult<Void> delete(@Valid @RequestBody DagTemplateGetReq req) {
        dagTemplateManager.delete(req.getId());
        return CommonResult.success();
    }

    /**
     * 查询DAG模板列表
     */
    @PostMapping("/list")
    public CommonResult<List<DagTemplateVO>> list(@Valid @RequestBody DagTemplateQuery query) {
        List<DagTemplateDTO> list = dagTemplateManager.list(query);
        return CommonResult.success(list.stream()
                .map(dagTemplateWebConvert::dto2vo)
                .collect(Collectors.toList()));
    }

    /**
     * 分页查询DAG模板
     */
    @PostMapping("/page")
    public CommonResult<PageResult<DagTemplateVO>> page(@RequestBody DagTemplatePageReq req) {
        // 转换请求对象为查询对象
        DagTemplateQuery query = dagTemplateWebConvert.req2query(req);
        // 执行分页查询
        PageResult<DagTemplateDTO> result = dagTemplateManager.page(query);
        return CommonResult.success(PageWarppers.box(result, dagTemplateWebConvert::dto2vo));
    }
} 