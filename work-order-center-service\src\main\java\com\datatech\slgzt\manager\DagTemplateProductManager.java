package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.DagTemplateProductDTO;
import com.datatech.slgzt.model.query.DagTemplateProductQuery;
import com.datatech.slgzt.utils.PageResult;
import java.util.List;

/**
 * DAG模板产品Manager接口
 */
public interface DagTemplateProductManager {

    /**
     * 创建DAG模板产品
     */
    void insert(DagTemplateProductDTO dto);

    /**
     * 更新DAG模板产品
     */
    void update(DagTemplateProductDTO dto);

    /**
     * 删除DAG模板产品
     */
    void delete(String id);

    /**
     * 根据ID查询
     */
    DagTemplateProductDTO getById(String id);

    /**
     * 根据模板ID查询列表
     */
    List<DagTemplateProductDTO> listByTemplateId(String templateId);

    /**
     * 列表查询
     */
    List<DagTemplateProductDTO> list(DagTemplateProductQuery query);

    /**
     * 分页查询
     */
    PageResult<DagTemplateProductDTO> page(DagTemplateProductQuery query);
} 