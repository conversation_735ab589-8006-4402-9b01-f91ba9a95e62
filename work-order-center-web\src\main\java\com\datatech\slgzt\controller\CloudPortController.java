package com.datatech.slgzt.controller;

import com.datatech.slgzt.annotation.OperationLog;
import com.datatech.slgzt.cloudPort.IWocCloudPortManager;
import com.datatech.slgzt.convert.CloudPortWebConvert;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.cloudPort.CloudPortDTO;
import com.datatech.slgzt.model.req.couldPort.CloudPortCreateReq;
import com.datatech.slgzt.model.req.couldPort.ClouldPortPageReq;
import com.datatech.slgzt.model.vo.cloudPort.CloudPortVO;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.warpper.PageWarppers;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 云端口的控制器
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月30日 20:59:23
 */
@RestController
@RequestMapping("/cloudPort")
public class CloudPortController {

    private final static Logger logger = LoggerFactory.getLogger(CloudPortController.class);

    @Resource
    private CloudPortWebConvert cloudPortWebConvert;

    @Resource
    private IWocCloudPortManager iWocCloudPortManager;

    /**
     * 分页查询
     */
    @PostMapping("/page")
    public CommonResult<PageResult<CloudPortVO>> page(@RequestBody ClouldPortPageReq req) {
        PageResult<CloudPortDTO> page = iWocCloudPortManager.page(cloudPortWebConvert.convert(req));
        PageResult<CloudPortVO> box = PageWarppers.box(page, cloudPortWebConvert::convert);
        return CommonResult.success(box);
    }


    /**
     * 新增
     */
    @PostMapping("/create")
    @OperationLog(description = "新增云端口", operationType = "CREATE")
    public CommonResult<Void> create(@RequestBody CloudPortCreateReq req) {
        // 校验必填参
        Precondition.checkArgument(StringUtils.isNotBlank(req.getRegionCode()), "云区域编码不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getPlatformCode()), "云平台编码不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getCloudPortName()), "云端口名称不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getVlanId()), "vlan Id不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getVpcId()), "VpcId不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getPeerIp()), "创建BGP的对端邻居地址不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getSrcIp()), "创建BGP的外部接口地址不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getPeerPassword()), "peer口令不能为空");
        CloudPortDTO dto = cloudPortWebConvert.convert(req);
        iWocCloudPortManager.create(dto);
        return CommonResult.success(null);
    }


    /**
     * 删除
     */
    @PostMapping("/delete")
    @OperationLog(description = "删除云端口", operationType = "DELETE")
    public CommonResult<Void> delete(@RequestBody CloudPortCreateReq req) {
        iWocCloudPortManager.delete(req.getId());
        return CommonResult.success(null);
    }

    /**
     * 查询云端口详情
     * @param req
     * @return
     */
    @PostMapping("/detail")
    public CommonResult<CloudPortVO>queryWocCloudPortDetail(@RequestBody CloudPortCreateReq req){
        logger.info("查询详情接受到参数：{}",req);
        CloudPortDTO cloudPortDTO = iWocCloudPortManager.queryWocCloudPortDetail(req.getId());
        CloudPortVO convert = cloudPortWebConvert.convert(cloudPortDTO);
        return CommonResult.success(convert);
    }

}
