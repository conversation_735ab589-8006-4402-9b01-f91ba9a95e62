package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.DagTemplateProductMapper;
import com.datatech.slgzt.dao.model.DagTemplateProductDO;
import com.datatech.slgzt.model.query.DagTemplateProductQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class DagTemplateProductDAO {

    @Resource
    private DagTemplateProductMapper mapper;

    public DagTemplateProductDO getById(String id) {
        return mapper.selectById(id);
    }

    public void insert(DagTemplateProductDO dagTemplateProductDO) {
        mapper.insert(dagTemplateProductDO);
    }

    public void updateById(DagTemplateProductDO dagTemplateProductDO) {
        mapper.updateById(dagTemplateProductDO);
    }

    public void deleteById(String id) {
        mapper.deleteById(id);
    }

    public List<DagTemplateProductDO> listByTemplateId(String templateId) {
        return mapper.selectList(Wrappers.<DagTemplateProductDO>lambdaQuery()
                                         .eq(DagTemplateProductDO::getTemplateId, templateId));
    }

    public List<DagTemplateProductDO> list(DagTemplateProductQuery query) {
        return mapper.selectList(Wrappers.<DagTemplateProductDO>lambdaQuery()
                                         .eq(ObjNullUtils.isNotNull(query.getId()), DagTemplateProductDO::getId, query.getId())
                                         .eq(ObjNullUtils.isNotNull(query.getTemplateId()), DagTemplateProductDO::getTemplateId, query.getTemplateId())
                                         .like(ObjNullUtils.isNotNull(query.getTemplateName()), DagTemplateProductDO::getTemplateName, query.getTemplateName())
                                         .eq(ObjNullUtils.isNotNull(query.getBusinessSystemId()), DagTemplateProductDO::getBusinessSystemId, query.getBusinessSystemId())
                                         .like(ObjNullUtils.isNotNull(query.getBusinessSystemName()), DagTemplateProductDO::getBusinessSystemName, query.getBusinessSystemName())
                                         .orderByDesc(DagTemplateProductDO::getCreateTime)
        );
    }

} 