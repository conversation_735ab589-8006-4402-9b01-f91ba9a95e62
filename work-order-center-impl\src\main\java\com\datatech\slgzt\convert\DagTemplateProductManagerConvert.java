package com.datatech.slgzt.convert;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.dao.model.DagTemplateProductDO;
import com.datatech.slgzt.model.dto.DagTemplateProductDTO;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.mapstruct.Mapper;

/**
 * DAG模板产品Manager层对象转换器
 */
@Mapper(componentModel = "spring")
public interface DagTemplateProductManagerConvert {
    
    /**
     * DTO转DO
     */
    default DagTemplateProductDO dto2do(DagTemplateProductDTO dto) {
        if (ObjNullUtils.isNull(dto)) {
            return null;
        }
        DagTemplateProductDO entity = new DagTemplateProductDO();
        entity.setId(dto.getId());
        entity.setTemplateId(dto.getTemplateId());
        entity.setTemplateName(dto.getTemplateName());
        entity.setDomainCodeList(JSON.toJSONString(dto.getDomainCodeList()));
        entity.setCatalogueDomainCodeList(JSON.toJSONString(dto.getCatalogueDomainCodeList()));
        entity.setReginModelList(JSON.toJSONString(dto.getReginModelList()));
        entity.setBusinessSystemId(dto.getBusinessSystemId());
        entity.setBusinessSystemName(dto.getBusinessSystemName());
        return entity;
    }

    /**
     * DO转DTO
     */
    default DagTemplateProductDTO do2dto(DagTemplateProductDO doo) {
        if (ObjNullUtils.isNull(doo)) {
            return null;
        }
        DagTemplateProductDTO dto = new DagTemplateProductDTO();
        dto.setId(doo.getId());
        dto.setTemplateId(doo.getTemplateId());
        dto.setTemplateName(doo.getTemplateName());
        dto.setDomainCodeList(JSON.parseArray(doo.getDomainCodeList(), String.class));
        dto.setCatalogueDomainCodeList(JSON.parseArray(doo.getCatalogueDomainCodeList(), String.class));
        dto.setReginModelList(JSON.parseArray(doo.getReginModelList(), DagTemplateProductDTO.ReginModel.class));
        dto.setBusinessSystemId(doo.getBusinessSystemId());
        dto.setBusinessSystemName(doo.getBusinessSystemName());
        return dto;
    }
} 