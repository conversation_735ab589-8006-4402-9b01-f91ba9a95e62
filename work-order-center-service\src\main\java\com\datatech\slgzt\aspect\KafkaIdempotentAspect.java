package com.datatech.slgzt.aspect;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.datatech.slgzt.annotation.KafkaIdempotent;
import com.datatech.slgzt.model.KafkaMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Kafka消息幂等处理切面
 */
@Slf4j
@Aspect
@Component
public class KafkaIdempotentAspect {

    @Autowired
    private RedissonClient redissonClient;
    @Around("@annotation(com.datatech.slgzt.annotation.KafkaIdempotent)")
    public Object handleBatchIdempotency(ProceedingJoinPoint point) throws Throwable {
        Object[] args = point.getArgs();
        if (args == null || args.length == 0) {
            return point.proceed();
        }

        // 获取方法和注解信息
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        KafkaIdempotent idempotent = method.getAnnotation(KafkaIdempotent.class);
        // 检查是否是批量处理
        if (args[0] instanceof List) {
            List<?> records = (List<?>) args[0];
            for (Object record : records) {
                if (record instanceof ConsumerRecord) {
                    ConsumerRecord<?, ?> consumerRecord = (ConsumerRecord<?, ?>) record;
                    KafkaMessage<?> message = JSONObject.parseObject(consumerRecord.value().toString(),
                            new TypeReference<KafkaMessage<?>>() {});
                    if (message != null) {
                        handleSingleMessage(point, idempotent, message);
                    }
                }
            }
            return null; // 批量处理返回 null
        }
        // 如果不是批量处理，直接处理单条消息
        return point.proceed();
    }

    private void handleSingleMessage(ProceedingJoinPoint point, KafkaIdempotent idempotent, KafkaMessage<?> message) throws Throwable {
        String idempotentKey =message.getMessageId();
        RLock lock = redissonClient.getLock(idempotentKey);

        try {
            if (!lock.tryLock(5, TimeUnit.SECONDS)) {
                log.warn("获取幂等锁失败，消息可能正在处理中：{}", idempotentKey);
                return;
            }

            if (redissonClient.getBucket(idempotentKey).isExists()) {
                log.info("消息已处理，跳过重复处理：{}", idempotentKey);
                return;
            }

            Object result = point.proceed();
            redissonClient.getBucket(idempotentKey).set("", idempotent.timeout(), TimeUnit.SECONDS);
            log.info("消息处理完成并设置幂等标记：{}", idempotentKey);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
} 