package com.datatech.slgzt.consumer;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.datatech.slgzt.annotation.KafkaIdempotent;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.manager.DagOrderManager;
import com.datatech.slgzt.manager.DagProductManager;
import com.datatech.slgzt.model.BatchRestartModel;
import com.datatech.slgzt.model.KafkaMessage;
import com.datatech.slgzt.model.dto.DagOrderDTO;
import com.datatech.slgzt.model.dto.DagProductDTO;
import com.datatech.slgzt.utils.Precondition;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.explore.JobExplorer;
import org.springframework.batch.core.launch.JobOperator;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月04日 10:47:27
 */
@Slf4j
@Component
public class BatchRestartConsumer {

    @Resource
    private DagProductManager dagProductManager;
    @Resource
    private DagOrderManager dagOrderManager;

    @Resource
    private JobOperator jobOperator;

    @Resource
    private JobExplorer jobExplorer;

    public final static String BATCH_RESTART = "woc_batch_restart_topic";

    @KafkaIdempotent
    @KafkaListener(groupId = "batch-restart-group", topics = {BATCH_RESTART})
    public void consumeMessage(List<ConsumerRecord<String, String>> consumerRecordList) {
        log.info("batch重启任务消息: {}", consumerRecordList.size());
        for (ConsumerRecord<String, String> record : consumerRecordList) {
            KafkaMessage<BatchRestartModel> messageRecord = JSONObject.parseObject(record.value()
                    , new TypeReference<KafkaMessage<BatchRestartModel>>() {
                    });
            BatchRestartModel batchRestartModel = messageRecord.getPayload();
            if (batchRestartModel == null) {
                log.warn("BatchRestartModel is null, skipping processing.");
                return;
            }
            if (!batchRestartModel.isRestartOnly()) {
                //更新产品表
                subscribeLayout(batchRestartModel);
                //如果消息是失败不需要重启
                if (batchRestartModel.getOpenStatus().equals(ResOpenEnum.OPEN_FAIL.getCode())) {
                    return;
                }
            }
            String jobId = batchRestartModel.getJobId();
            if (jobId == null || jobId.isEmpty()) {
                log.warn("Job ID is null or empty, skipping processing.");
                return;
            }
            log.info("Processing Batch Restart for Job ID: {}", jobId);
            // 这里可以添加重启批次的逻辑
            restartBatchJob(Long.valueOf(jobId));
        }
    }

    private void subscribeLayout(BatchRestartModel req) {
        if (req.getOrderType() == 1) {
            //如果类型是vpc,就要通过vpc去找 product
            DagProductDTO productDTO = dagProductManager.getBySubOrderId(Long.valueOf(req.getSubOrderId()));
            Precondition.checkArgument(productDTO, "找不到对应产品");
            DagOrderDTO orderDTO = dagOrderManager.getById(productDTO.getOrderId());
            Precondition.checkArgument(orderDTO, "找不到对应工单");
            //更新对应的状态
            productDTO.setMessage(req.getMessage());
            productDTO.setOpenStatus(req.getOpenStatus());
            dagProductManager.update(productDTO);
        }
        if (req.getOrderType() == 2) {
            log.info("子产品回调 不处理等主回调 req={}", JSONObject.toJSONString(req));
        }
    }

    @SneakyThrows
    @Async
    public void restartBatchJob(Long jobId) {
        // 实现重启批次的逻辑
        // 如果执行ID不为空，说明是重试
        JobExecution jobExecution = jobExplorer.getJobExecution(jobId);
        if (jobExecution == null) {
            log.warn("非法的任务ID找不到对应的执行实例 jobId: {}", jobId);
            return;
        }
        Collection<StepExecution> stepExecutions = jobExecution.getStepExecutions();
        if (!stepExecutions.isEmpty()) {
            for (StepExecution stepExecution : stepExecutions) {
                log.info("当前执行步骤: {}, 状态: {}", stepExecution.getStepName(), stepExecution.getStatus());
            }
        }
        // 如果存在，则直接返回
        BatchStatus status = jobExecution.getStatus();
        if (status == BatchStatus.COMPLETED) {
            // 如果状态是完成，则直接返回
            log.info("Job already completed for jobId: {}", jobId);
        } else if (status == BatchStatus.FAILED) {
            // 如果状态是失败，则重新启动
            jobOperator.restart(jobId);
            log.info("Job restarted successfully for jobId: {}", jobId);
        } else if (status == BatchStatus.STOPPED) {
            // 如果状态是停止，则重新启动
            jobOperator.restart(jobId);
            log.info("Job restarted successfully for jobId: {}", jobId);
        }
    }

}
