package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.DagProductMapper;
import com.datatech.slgzt.dao.model.DagProductDO;
import com.datatech.slgzt.model.query.DagProductQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class DagProductDAO {

    @Resource
    private DagProductMapper mapper;

    public DagProductDO getById(String id) {
        return mapper.selectById(id);
    }

    public void insert(DagProductDO dagProductDO) {
        mapper.insert(dagProductDO);
    }

    public void updateById(DagProductDO dagProductDO) {
        mapper.updateById(dagProductDO);
    }

    public void deleteById(String id) {
        mapper.deleteById(id);
    }

    public List<DagProductDO> list(DagProductQuery query) {
        return mapper.selectList(Wrappers.<DagProductDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getProductType()), DagProductDO::getProductType, query.getProductType())
                .eq(ObjNullUtils.isNotNull(query.getOrderId()), DagProductDO::getOrderId, query.getOrderId())
                .in(ObjNullUtils.isNotNull(query.getStatusList()), DagProductDO::getStatus, query.getStatusList())
                .eq(ObjNullUtils.isNotNull(query.getStatus()), DagProductDO::getStatus, query.getStatus())
                .orderByDesc(DagProductDO::getCreateTime)
        );
    }

    public DagProductDO getBySubOrderId(Long subOrderId) {
        return mapper.selectOne(Wrappers.<DagProductDO>lambdaQuery()
               .eq(DagProductDO::getSubOrderId, subOrderId));
    }

    public DagProductDO getByVpcId(String vpcId) {
        return mapper.selectOne(Wrappers.<DagProductDO>lambdaQuery()
              .eq(DagProductDO::getVpcId, vpcId));
    }

    public void updateByParentId(DagProductDO product) {
        mapper.update(product, Wrappers.<DagProductDO>lambdaUpdate()
                .eq(DagProductDO::getParentProductId
                        , product.getParentProductId()));
    }
} 