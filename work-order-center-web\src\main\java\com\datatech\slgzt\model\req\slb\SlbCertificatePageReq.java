package com.datatech.slgzt.model.req.slb;

import com.datatech.slgzt.utils.ObjNullUtils;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * SLB证书分页查询请求
 */
@Data
public class SlbCertificatePageReq {

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 证书名称
     */
    private String certificateName;

    /**
     * 证书类型
     */
    private String certificateType;

    /**
     * 域名
     */
    private String domainCode;


    /**
     * 业务系统id
     */
    private String businessSystemId;

    /**
     * 云类型
     */
    private String catalogueDomainCode;


    /**
     * 资源池Id
     */
    private String regionId;


    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;


} 